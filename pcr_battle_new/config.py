"""
插件配置文件

定义游戏的各种配置参数
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class GameConfig:
    """游戏配置"""
    # 房间设置
    max_players: int = 4
    min_players: int = 2
    room_timeout: int = 300  # 房间超时时间（秒）
    
    # 回合设置
    turn_timeout: int = 30  # 回合超时时间（秒）
    dice_sides: int = 6  # 色子面数
    
    # 跑道设置
    runway_size: int = 36  # 跑道格子数
    
    # 战斗设置
    base_tp_gain: int = 10  # 每回合基础TP恢复
    hit_down_tp_gain: int = 20  # 击倒敌人获得的TP
    round_attack_bonus: int = 10  # 每轮攻击力增加
    round_distance_bonus: int = 2  # 每轮攻击距离增加
    
    # 属性限制
    max_health: int = 9999
    max_attack: int = 999
    max_defense: int = 999
    max_distance: int = 15
    max_crit_rate: int = 100
    max_tp: int = 100
    
    # 图片渲染设置
    image_width: int = 800
    image_height: int = 600
    grid_size: int = 50


@dataclass
class DebugConfig:
    """调试配置"""
    enable_debug_mode: bool = False
    log_level: str = "INFO"
    enable_performance_monitoring: bool = False
    enable_event_logging: bool = False


# 全局配置实例
game_config = GameConfig()
debug_config = DebugConfig()


def load_config(config_dict: Dict[str, Any]):
    """从字典加载配置"""
    global game_config, debug_config
    
    # 更新游戏配置
    if "game" in config_dict:
        game_data = config_dict["game"]
        for key, value in game_data.items():
            if hasattr(game_config, key):
                setattr(game_config, key, value)
    
    # 更新调试配置
    if "debug" in config_dict:
        debug_data = config_dict["debug"]
        for key, value in debug_data.items():
            if hasattr(debug_config, key):
                setattr(debug_config, key, value)


def get_config() -> Dict[str, Any]:
    """获取当前配置"""
    return {
        "game": game_config.__dict__,
        "debug": debug_config.__dict__
    }


# 默认配置
DEFAULT_CONFIG = {
    "game": {
        "max_players": 4,
        "min_players": 2,
        "room_timeout": 300,
        "turn_timeout": 30,
        "dice_sides": 6,
        "runway_size": 36,
        "base_tp_gain": 10,
        "hit_down_tp_gain": 20,
        "round_attack_bonus": 10,
        "round_distance_bonus": 2,
        "max_health": 9999,
        "max_attack": 999,
        "max_defense": 999,
        "max_distance": 15,
        "max_crit_rate": 100,
        "max_tp": 100,
        "image_width": 800,
        "image_height": 600,
        "grid_size": 50
    },
    "debug": {
        "enable_debug_mode": False,
        "log_level": "INFO",
        "enable_performance_monitoring": False,
        "enable_event_logging": False
    }
}
