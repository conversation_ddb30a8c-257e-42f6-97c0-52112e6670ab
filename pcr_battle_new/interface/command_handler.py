"""
命令处理器

处理来自NoneBot的各种命令
"""

import re
import logging

from nonebot.adapters.onebot.v11 import <PERSON><PERSON>, GroupMessageEvent, MessageSegment

from ..game.room_manager import room_manager
from ..game.turn_system import TurnSystem
from ..game.battle_system import BattleSystem
from ..game.runway_system import RunwaySystem
from ..character.character_data import get_all_characters
from ..core.state_manager import GameState, PlayerState, state_manager
from .message_builder import MessageBuilder
from .image_renderer import ImageRenderer

logger = logging.getLogger(__name__)


class CommandHandler:
    """命令处理器"""
    
    def __init__(self):
        self.message_builder = MessageBuilder()
        self.image_renderer = ImageRenderer()
    
    async def handle_create_game(self, bot: Bot, event: GroupMessageEvent):
        """处理创建游戏命令"""
        group_id = str(event.group_id)
        user_id = str(event.user_id)
        
        # 检查是否已经在游戏中
        existing_room = room_manager.get_user_room(user_id)
        if existing_room:
            await bot.send(event, "你已经在游戏房间中了！")
            return
        
        # 检查群组是否已有游戏
        existing_group_room = room_manager.get_room(group_id)
        if existing_group_room:
            await bot.send(event, "本群已有游戏正在进行中！")
            return
        
        # 创建房间
        room = room_manager.create_room(group_id, user_id)
        if room:
            room.state_machine.transition_to(GameState.WAITING)
            
            message = self.message_builder.build_room_created_message(room)
            await bot.send(event, message)
            
            logger.info(f"Game room created in group {group_id} by user {user_id}")
        else:
            await bot.send(event, "创建游戏失败！")
    
    async def handle_join_game(self, bot: Bot, event: GroupMessageEvent):
        """处理加入游戏命令"""
        group_id = str(event.group_id)
        user_id = str(event.user_id)
        
        # 获取用户昵称
        try:
            member_info = await bot.get_group_member_info(group_id=event.group_id, user_id=event.user_id)
            nickname = member_info.get('card') or member_info.get('nickname', f"Player_{user_id}")
        except:
            nickname = f"Player_{user_id}"
        
        # 检查是否已经在游戏中
        existing_room = room_manager.get_user_room(user_id)
        if existing_room:
            await bot.send(event, "你已经在游戏房间中了！")
            return
        
        # 加入房间
        success = room_manager.join_room(group_id, user_id, nickname)
        if success:
            room = room_manager.get_room(group_id)
            message = self.message_builder.build_player_joined_message(room, nickname)
            await bot.send(event, message)
            
            # 如果人数满了，提示可以开始游戏
            if len(room.players) >= room.max_players:
                await bot.send(event, "人数已满！房主可以发送\"开始大乱斗\"开始游戏")
        else:
            await bot.send(event, "加入游戏失败！可能是房间已满或不存在")
    
    async def handle_start_game(self, bot: Bot, event: GroupMessageEvent):
        """处理开始游戏命令"""
        group_id = str(event.group_id)
        user_id = str(event.user_id)
        
        room = room_manager.get_room(group_id)
        if not room:
            await bot.send(event, "没有找到游戏房间！")
            return
        
        # 检查是否是房主
        if user_id != room.creator_id:
            await bot.send(event, "只有房主可以开始游戏！")
            return
        
        # 检查人数
        if len(room.players) < 2:
            await bot.send(event, "至少需要2个玩家才能开始游戏！")
            return
        
        # 转换到角色选择阶段
        if room.state_machine.transition_to(GameState.CHARACTER_SELECT):
            message = self.message_builder.build_character_select_message()
            await bot.send(event, message)
        else:
            await bot.send(event, "无法开始游戏，请检查游戏状态！")
    
    async def handle_end_game(self, bot: Bot, event: GroupMessageEvent):
        """处理结束游戏命令"""
        group_id = str(event.group_id)
        user_id = str(event.user_id)
        
        room = room_manager.get_room(group_id)
        if not room:
            await bot.send(event, "没有找到游戏房间！")
            return
        
        # 检查是否是房主
        if user_id != room.creator_id:
            await bot.send(event, "只有房主可以结束游戏！")
            return
        
        # 结束游戏
        room.end_game()
        room_manager.destroy_room(group_id)
        
        await bot.send(event, "游戏已结束！")
    
    async def handle_game_status(self, bot: Bot, event: GroupMessageEvent):
        """处理查看状态命令"""
        user_id = str(event.user_id)
        
        room = room_manager.get_user_room(user_id)
        if not room:
            await bot.send(event, "你不在任何游戏房间中！")
            return
        
        message = self.message_builder.build_game_status_message(room, user_id)
        await bot.send(event, message)
        
        # 如果游戏正在进行，发送游戏画面
        if room.state_machine.current_state == GameState.PLAYING:
            try:
                image = self.image_renderer.render_game_board(room)
                if image:
                    await bot.send(event, MessageSegment.image(image))
            except Exception as e:
                logger.error(f"Failed to render game board: {e}")
    
    async def handle_game_rules(self, bot: Bot, event: GroupMessageEvent):
        """处理游戏规则命令"""
        message = self.message_builder.build_rules_message()
        await bot.send(event, message)
    
    async def handle_character_list(self, bot: Bot, event: GroupMessageEvent):
        """处理角色列表命令"""
        characters = get_all_characters()
        message = self.message_builder.build_character_list_message(characters)
        await bot.send(event, message)
    
    async def handle_surrender(self, bot: Bot, event: GroupMessageEvent):
        """处理认输命令"""
        user_id = str(event.user_id)
        
        room = room_manager.get_user_room(user_id)
        if not room:
            await bot.send(event, "你不在任何游戏房间中！")
            return
        
        if room.state_machine.current_state != GameState.PLAYING:
            await bot.send(event, "游戏还没开始！")
            return
        
        # 将玩家设为失败
        if user_id in room.players:
            player_info = room.players[user_id]
            if player_info.character_entity:
                from ..character.components import AttributeComponent
                from ..character.character_data import AttributeType
                
                attr_component = player_info.character_entity.get_component(AttributeComponent)
                if attr_component:
                    attr_component.modify_attribute(AttributeType.CURRENT_HEALTH, -9999)
        
        await bot.send(event, f"{MessageSegment.at(event.user_id)} 认输了！")
        
        # 检查游戏是否结束
        alive_players = room.get_alive_players()
        if len(alive_players) <= 1:
            winner = alive_players[0] if alive_players else None
            room.end_game(winner)
            
            if winner:
                winner_name = room.players[winner].nickname
                await bot.send(event, f"游戏结束！获胜者是：{winner_name}")
            else:
                await bot.send(event, "游戏结束！没有获胜者")
    
    async def handle_throw_dice(self, bot: Bot, event: GroupMessageEvent):
        """处理丢色子命令"""
        user_id = str(event.user_id)
        
        room = room_manager.get_user_room(user_id)
        if not room:
            await bot.send(event, "你不在任何游戏房间中！")
            return
        
        if room.state_machine.current_state != GameState.PLAYING:
            await bot.send(event, "游戏还没开始！")
            return
        
        # 检查是否轮到该玩家
        current_player = room.get_current_player()
        if current_player != user_id:
            await bot.send(event, "还没轮到你的回合！")
            return
        
        # 检查玩家状态
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if not player_state_machine or player_state_machine.current_state != PlayerState.TURN_DICE:
            await bot.send(event, "现在不能丢色子！")
            return
        
        # 丢色子
        turn_system = TurnSystem(room)
        dice_result = turn_system.throw_dice(user_id)
        
        if dice_result:
            player_name = room.players[user_id].nickname
            new_position = room.players[user_id].position
            
            await bot.send(event, f"{player_name} 投出了 {dice_result} 点，移动到位置 {new_position}")
            
            # 触发跑道事件
            runway_system = RunwaySystem(room)
            event_messages = runway_system.trigger_event(user_id, new_position)
            
            if event_messages:
                await bot.send(event, "\n".join(event_messages))
            
            # 发送更新后的游戏画面
            try:
                image = self.image_renderer.render_game_board(room)
                if image:
                    await bot.send(event, MessageSegment.image(image))
            except Exception as e:
                logger.error(f"Failed to render game board: {e}")
            
            # 提示可以使用技能
            await bot.send(event, f"{player_name} 现在可以使用技能了！发送\"技能 [技能编号] [目标]\"来使用技能")
        else:
            await bot.send(event, "丢色子失败！")
    
    async def handle_use_skill(self, bot: Bot, event: GroupMessageEvent):
        """处理使用技能命令"""
        user_id = str(event.user_id)
        
        room = room_manager.get_user_room(user_id)
        if not room:
            await bot.send(event, "你不在任何游戏房间中！")
            return
        
        if room.state_machine.current_state != GameState.PLAYING:
            await bot.send(event, "游戏还没开始！")
            return
        
        # 解析命令参数
        message_text = str(event.get_message()).strip()
        parts = message_text.split()
        
        if len(parts) < 2:
            await bot.send(event, "请指定技能编号！格式：技能 [编号] [目标(可选)]")
            return
        
        try:
            skill_index = int(parts[1]) - 1  # 转换为0基索引
        except ValueError:
            await bot.send(event, "技能编号必须是数字！")
            return
        
        # 获取目标
        target_id = None
        if len(parts) >= 3:
            # 尝试解析@目标
            at_match = re.search(r'\[CQ:at,qq=(\d+)\]', parts[2])
            if at_match:
                target_id = at_match.group(1)
            else:
                # 尝试按昵称查找
                target_name = parts[2]
                for pid, player_info in room.players.items():
                    if player_info.nickname == target_name:
                        target_id = pid
                        break
        
        # 使用技能
        battle_system = BattleSystem(room)
        
        # 获取玩家的技能列表
        skills_info = battle_system.get_all_skills(user_id)
        active_skills = skills_info.get("active_skills", [])
        
        if skill_index < 0 or skill_index >= len(active_skills):
            await bot.send(event, f"技能编号无效！请选择1-{len(active_skills)}之间的技能")
            return
        
        skill_id = active_skills[skill_index]["id"]
        result = battle_system.use_skill(user_id, skill_id, target_id)
        
        if result:
            if result.success:
                messages = [f"技能使用成功！"] + result.messages
                await bot.send(event, "\n".join(messages))
                
                # 发送更新后的游戏画面
                try:
                    image = self.image_renderer.render_game_board(room)
                    if image:
                        await bot.send(event, MessageSegment.image(image))
                except Exception as e:
                    logger.error(f"Failed to render game board: {e}")
                
                # 切换到下一个玩家
                turn_system = TurnSystem(room)
                next_player = turn_system.next_turn()
                
                if next_player:
                    next_player_name = room.players[next_player].nickname
                    await bot.send(event, f"轮到 {next_player_name} 的回合了！")
                else:
                    # 游戏结束
                    if room.winner:
                        winner_name = room.players[room.winner].nickname
                        await bot.send(event, f"游戏结束！获胜者是：{winner_name}")
                    else:
                        await bot.send(event, "游戏结束！")
            else:
                await bot.send(event, f"技能使用失败：{result.error_message}")
        else:
            await bot.send(event, "技能使用失败！")
    
    async def handle_select_character(self, bot: Bot, event: GroupMessageEvent):
        """处理选择角色命令"""
        user_id = str(event.user_id)
        message_text = str(event.get_message()).strip()
        
        room = room_manager.get_user_room(user_id)
        if not room:
            return  # 不在游戏中，忽略消息
        
        if room.state_machine.current_state != GameState.CHARACTER_SELECT:
            return  # 不在角色选择阶段，忽略消息
        
        # 尝试匹配角色名称
        characters = get_all_characters()
        selected_character = None
        
        for char_id, char_data in characters.items():
            if char_data.name in message_text or char_id in message_text:
                selected_character = char_id
                break
        
        if selected_character:
            success = room.select_character(user_id, selected_character)
            if success:
                char_data = characters[selected_character]
                await bot.send(event, f"你选择了角色：{char_data.name}")
                
                # 检查是否所有玩家都选择了角色
                all_ready = all(player.is_ready for player in room.players.values())
                if all_ready:
                    # 开始游戏
                    if room.start_game():
                        await bot.send(event, "所有玩家都选择了角色，游戏开始！")
                        
                        # 发送游戏画面
                        try:
                            image = self.image_renderer.render_game_board(room)
                            if image:
                                await bot.send(event, MessageSegment.image(image))
                        except Exception as e:
                            logger.error(f"Failed to render game board: {e}")
                        
                        # 开始第一个玩家的回合
                        turn_system = TurnSystem(room)
                        first_player = room.get_current_player()
                        if first_player:
                            turn_system.start_turn(first_player)
                            player_name = room.players[first_player].nickname
                            await bot.send(event, f"轮到 {player_name} 的回合了！请丢色子")
            else:
                await bot.send(event, "选择角色失败！可能角色已被选择或不存在")
