"""
图片渲染器

生成游戏画面图片
"""

from typing import Optional, Dict, Any
import io
import base64
from PIL import Image, ImageDraw, ImageFont
import logging

from ..game.room_manager import GameRoom
from ..game.runway_system import RunwaySystem
from ..character.character_factory import get_character_info

logger = logging.getLogger(__name__)


class ImageRenderer:
    """图片渲染器"""
    
    def __init__(self):
        self.board_size = (800, 600)
        self.grid_size = 50
        self.colors = {
            'background': (255, 255, 255),
            'runway': (200, 200, 200),
            'player': (100, 150, 255),
            'current_player': (255, 100, 100),
            'text': (0, 0, 0),
            'health_bar': (30, 230, 100),
            'tp_bar': (30, 144, 255),
            'health_bg': (200, 200, 200),
            'tp_bg': (200, 200, 200)
        }
        
        # 尝试加载字体
        try:
            self.font = ImageFont.truetype("arial.ttf", 12)
            self.small_font = ImageFont.truetype("arial.ttf", 10)
        except:
            try:
                # 尝试系统默认字体
                self.font = ImageFont.load_default()
                self.small_font = ImageFont.load_default()
            except:
                self.font = None
                self.small_font = None
    
    def render_game_board(self, room: GameRoom) -> Optional[str]:
        """渲染游戏画面"""
        try:
            # 创建画布
            image = Image.new('RGB', self.board_size, self.colors['background'])
            draw = ImageDraw.Draw(image)
            
            # 绘制跑道
            self._draw_runway(draw, room)
            
            # 绘制玩家
            self._draw_players(draw, room)
            
            # 绘制玩家信息面板
            self._draw_player_info(draw, room)
            
            # 绘制游戏信息
            self._draw_game_info(draw, room)
            
            # 转换为base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            buffer.seek(0)
            
            return base64.b64encode(buffer.getvalue()).decode()
            
        except Exception as e:
            logger.error(f"Failed to render game board: {e}")
            return None
    
    def _draw_runway(self, draw: ImageDraw.Draw, room: GameRoom):
        """绘制跑道"""
        center_x, center_y = self.board_size[0] // 2, self.board_size[1] // 2
        radius = 150
        
        # 计算跑道位置
        runway_positions = []
        for i in range(room.runway_size):
            angle = (i / room.runway_size) * 2 * 3.14159
            x = center_x + radius * (0.8 if i % 2 == 0 else 1.0) * (1 if i < room.runway_size // 2 else -1) * abs(angle - 3.14159) / 3.14159
            y = center_y + radius * (0.8 if i % 4 < 2 else 1.0) * (1 if i < room.runway_size // 4 or i >= 3 * room.runway_size // 4 else -1)
            
            # 简化为矩形跑道
            if i < room.runway_size // 4:  # 上边
                x = center_x - radius + (i * 2 * radius) // (room.runway_size // 4)
                y = center_y - radius
            elif i < room.runway_size // 2:  # 右边
                x = center_x + radius
                y = center_y - radius + ((i - room.runway_size // 4) * 2 * radius) // (room.runway_size // 4)
            elif i < 3 * room.runway_size // 4:  # 下边
                x = center_x + radius - ((i - room.runway_size // 2) * 2 * radius) // (room.runway_size // 4)
                y = center_y + radius
            else:  # 左边
                x = center_x - radius
                y = center_y + radius - ((i - 3 * room.runway_size // 4) * 2 * radius) // (room.runway_size // 4)
            
            runway_positions.append((x, y))
        
        # 绘制跑道格子
        runway_system = RunwaySystem(room)
        for i, (x, y) in enumerate(runway_positions):
            # 绘制格子
            rect = [x - 15, y - 15, x + 15, y + 15]
            draw.rectangle(rect, outline=self.colors['runway'], width=2)
            
            # 绘制格子编号
            if self.small_font:
                draw.text((x - 5, y - 5), str(i), fill=self.colors['text'], font=self.small_font)
            
            # 绘制事件标识
            event = runway_system.get_event(i)
            if event and event.event_type.value != "none":
                event_color = self._get_event_color(event.event_type.value)
                draw.rectangle([x - 12, y - 12, x + 12, y + 12], fill=event_color)
        
        self.runway_positions = runway_positions
    
    def _draw_players(self, draw: ImageDraw.Draw, room: GameRoom):
        """绘制玩家"""
        current_player = room.get_current_player()
        
        for user_id, player_info in room.players.items():
            if not player_info.character_entity:
                continue
            
            position = player_info.position
            if position < len(self.runway_positions):
                x, y = self.runway_positions[position]
                
                # 选择颜色
                color = self.colors['current_player'] if user_id == current_player else self.colors['player']
                
                # 绘制玩家圆圈
                draw.ellipse([x - 8, y - 8, x + 8, y + 8], fill=color)
                
                # 绘制玩家名称
                if self.small_font:
                    name = player_info.nickname[:4]  # 限制长度
                    draw.text((x - 10, y + 10), name, fill=self.colors['text'], font=self.small_font)
    
    def _draw_player_info(self, draw: ImageDraw.Draw, room: GameRoom):
        """绘制玩家信息面板"""
        panel_x = 10
        panel_y = 10
        panel_width = 200
        panel_height = 120
        
        y_offset = panel_y
        
        for user_id, player_info in room.players.items():
            if not player_info.character_entity:
                continue
            
            # 获取角色信息
            char_info = get_character_info(player_info.character_entity)
            if not char_info:
                continue
            
            attributes = char_info.get("attributes", {})
            
            # 绘制玩家面板背景
            draw.rectangle([panel_x, y_offset, panel_x + panel_width, y_offset + panel_height], 
                         outline=self.colors['text'], width=1)
            
            # 玩家名称和角色
            if self.font:
                name_text = f"{player_info.nickname} - {char_info.get('name', '未知')}"
                draw.text((panel_x + 5, y_offset + 5), name_text, fill=self.colors['text'], font=self.font)
            
            # 生命值条
            current_health = attributes.get('current_health', 0)
            max_health = attributes.get('max_health', 1)
            health_ratio = current_health / max_health if max_health > 0 else 0
            
            health_bar_y = y_offset + 25
            draw.rectangle([panel_x + 5, health_bar_y, panel_x + panel_width - 5, health_bar_y + 15], 
                         fill=self.colors['health_bg'])
            draw.rectangle([panel_x + 5, health_bar_y, 
                          panel_x + 5 + (panel_width - 10) * health_ratio, health_bar_y + 15], 
                         fill=self.colors['health_bar'])
            
            if self.small_font:
                health_text = f"HP: {current_health:.0f}/{max_health:.0f}"
                draw.text((panel_x + 8, health_bar_y + 2), health_text, fill=self.colors['text'], font=self.small_font)
            
            # TP条
            current_tp = attributes.get('current_tp', 0)
            max_tp = attributes.get('max_tp', 100)
            tp_ratio = current_tp / max_tp if max_tp > 0 else 0
            
            tp_bar_y = y_offset + 45
            draw.rectangle([panel_x + 5, tp_bar_y, panel_x + panel_width - 5, tp_bar_y + 15], 
                         fill=self.colors['tp_bg'])
            draw.rectangle([panel_x + 5, tp_bar_y, 
                          panel_x + 5 + (panel_width - 10) * tp_ratio, tp_bar_y + 15], 
                         fill=self.colors['tp_bar'])
            
            if self.small_font:
                tp_text = f"TP: {current_tp:.0f}/{max_tp:.0f}"
                draw.text((panel_x + 8, tp_bar_y + 2), tp_text, fill=self.colors['text'], font=self.small_font)
            
            # 其他属性
            if self.small_font:
                attack = attributes.get('attack', 0)
                defense = attributes.get('defense', 0)
                distance = attributes.get('distance', 0)
                
                attr_text = f"攻击:{attack:.0f} 防御:{defense:.0f} 距离:{distance:.0f}"
                draw.text((panel_x + 5, y_offset + 65), attr_text, fill=self.colors['text'], font=self.small_font)
                
                # 位置信息
                pos_text = f"位置: {player_info.position}"
                draw.text((panel_x + 5, y_offset + 80), pos_text, fill=self.colors['text'], font=self.small_font)
            
            y_offset += panel_height + 10
    
    def _draw_game_info(self, draw: ImageDraw.Draw, room: GameRoom):
        """绘制游戏信息"""
        info_x = self.board_size[0] - 200
        info_y = 10
        
        if self.font:
            # 回合信息
            current_player = room.get_current_player()
            if current_player:
                current_name = room.players[current_player].nickname
                turn_text = f"当前回合: {current_name}"
                draw.text((info_x, info_y), turn_text, fill=self.colors['text'], font=self.font)
            
            # 回合数
            round_text = f"回合数: {room.turn_count}"
            draw.text((info_x, info_y + 20), round_text, fill=self.colors['text'], font=self.font)
            
            # 存活玩家数
            alive_count = len(room.get_alive_players())
            alive_text = f"存活玩家: {alive_count}/{len(room.players)}"
            draw.text((info_x, info_y + 40), alive_text, fill=self.colors['text'], font=self.font)
    
    def _get_event_color(self, event_type: str) -> tuple:
        """获取事件颜色"""
        color_map = {
            'health': (100, 255, 100),
            'attack': (255, 100, 100),
            'defense': (100, 100, 255),
            'tp': (255, 255, 100),
            'move': (255, 100, 255),
            'buff': (100, 255, 255),
            'trap': (255, 50, 50)
        }
        return color_map.get(event_type, (150, 150, 150))
