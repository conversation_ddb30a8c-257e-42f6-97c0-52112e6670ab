"""
消息构建器

构建各种游戏消息
"""

from typing import Dict, Any, List
from ..game.room_manager import GameRoom
from ..character.character_data import CharacterData, get_all_characters
from ..character.character_factory import get_character_info
from ..core.state_manager import GameState


class MessageBuilder:
    """消息构建器"""
    
    def build_room_created_message(self, room: GameRoom) -> str:
        """构建房间创建消息"""
        return f"""大乱斗房间已创建！
房间ID: {room.room_id}
房主: {room.players[room.creator_id].nickname}
当前人数: {len(room.players)}/{room.max_players}

发送"加入大乱斗"加入游戏
发送"开始大乱斗"开始游戏（房主）"""
    
    def build_player_joined_message(self, room: GameRoom, nickname: str) -> str:
        """构建玩家加入消息"""
        player_list = [player.nickname for player in room.players.values()]
        return f"""{nickname} 加入了游戏！
当前人数: {len(room.players)}/{room.max_players}
玩家列表: {', '.join(player_list)}"""
    
    def build_character_select_message(self) -> str:
        """构建角色选择消息"""
        characters = get_all_characters()
        char_list = []
        
        for char_data in characters.values():
            char_list.append(f"• {char_data.name} ({char_data.position.value})")
        
        return f"""请选择你的角色：
{chr(10).join(char_list)}

直接发送角色名称即可选择，如：日和"""
    
    def build_character_list_message(self, characters: Dict[str, CharacterData]) -> str:
        """构建角色列表消息"""
        message_parts = ["可选角色列表："]
        
        for char_data in characters.values():
            base_attrs = char_data.base_attributes
            health = base_attrs.get('max_health', 0)
            attack = base_attrs.get('attack', 0)
            defense = base_attrs.get('defense', 0)
            distance = base_attrs.get('distance', 0)
            
            char_info = f"""
{char_data.name} ({char_data.position.value})
├ 生命值: {health}
├ 攻击力: {attack}
├ 防御力: {defense}
└ 攻击距离: {distance}"""
            
            message_parts.append(char_info)
        
        return "\n".join(message_parts)
    
    def build_game_status_message(self, room: GameRoom, user_id: str) -> str:
        """构建游戏状态消息"""
        message_parts = [f"游戏状态 - 房间 {room.room_id}"]
        
        # 游戏阶段
        state_names = {
            GameState.WAITING: "等待玩家",
            GameState.CHARACTER_SELECT: "角色选择",
            GameState.PLAYING: "游戏进行中",
            GameState.ENDED: "游戏结束"
        }
        current_state = state_names.get(room.state_machine.current_state, "未知状态")
        message_parts.append(f"当前阶段: {current_state}")
        
        # 玩家信息
        if room.state_machine.current_state == GameState.PLAYING:
            current_player = room.get_current_player()
            if current_player:
                current_name = room.players[current_player].nickname
                message_parts.append(f"当前回合: {current_name}")
            
            message_parts.append(f"回合数: {room.turn_count}")
        
        # 玩家列表
        message_parts.append("\n玩家列表:")
        for player_id, player_info in room.players.items():
            status_icon = "🟢" if player_id in room.get_alive_players() else "🔴"
            char_name = "未选择"
            
            if player_info.character_entity:
                char_info = get_character_info(player_info.character_entity)
                char_name = char_info.get("name", "未知")
            
            message_parts.append(f"{status_icon} {player_info.nickname} - {char_name}")
        
        # 当前玩家详细信息
        if user_id in room.players and room.players[user_id].character_entity:
            message_parts.append(self._build_character_status(room.players[user_id].character_entity))
        
        return "\n".join(message_parts)
    
    def build_rules_message(self) -> str:
        """构建游戏规则消息"""
        return """PCR大乱斗规则：

🎯 游戏目标
成为最后的幸存者！

🎮 游戏流程
1. 创建房间并邀请好友加入
2. 选择你的角色
3. 轮流丢色子移动
4. 使用技能攻击其他玩家
5. 触发跑道事件获得增益或减益

⚔️ 战斗系统
• 每回合可以丢色子移动
• 移动后可以使用技能
• 技能需要消耗TP
• 每回合自动恢复TP

🏃 跑道系统
• 环形跑道，共36个格子
• 不同位置有不同事件
• 可能获得属性加成或减益

📋 命令列表
• 创建大乱斗 - 创建游戏房间
• 加入大乱斗 - 加入游戏
• 开始大乱斗 - 开始游戏（房主）
• 角色列表 - 查看可选角色
• 查看状态 - 查看游戏状态
• 丢色子 - 投掷色子移动
• 技能 [编号] [目标] - 使用技能
• 认输 - 主动认输"""
    
    def _build_character_status(self, character_entity) -> str:
        """构建角色状态信息"""
        char_info = get_character_info(character_entity)
        
        if not char_info:
            return "\n角色信息获取失败"
        
        attributes = char_info.get("attributes", {})
        skills = char_info.get("active_skills", [])
        buffs = char_info.get("buffs", [])
        
        status_parts = [f"\n你的角色: {char_info.get('name', '未知')}"]
        
        # 属性信息
        health = f"{attributes.get('current_health', 0):.0f}/{attributes.get('max_health', 0):.0f}"
        tp = f"{attributes.get('current_tp', 0):.0f}/{attributes.get('max_tp', 100):.0f}"
        
        status_parts.append(f"生命值: {health}")
        status_parts.append(f"TP: {tp}")
        status_parts.append(f"攻击力: {attributes.get('attack', 0):.0f}")
        status_parts.append(f"防御力: {attributes.get('defense', 0):.0f}")
        status_parts.append(f"攻击距离: {attributes.get('distance', 0):.0f}")
        
        # 技能信息
        if skills:
            status_parts.append("\n可用技能:")
            for i, skill in enumerate(skills, 1):
                can_use = "✅" if skill.get("can_use", False) else "❌"
                status_parts.append(f"{i}. {can_use} {skill.get('name', '未知')} (TP:{skill.get('tp_cost', 0)})")
        
        # Buff信息
        if buffs:
            status_parts.append("\n当前效果:")
            for buff in buffs:
                remaining = buff.get("remaining_turns", 0)
                duration_text = f"({remaining}回合)" if remaining > 0 else "(永久)"
                status_parts.append(f"• {buff.get('name', '未知')} {duration_text}")
        
        return "\n".join(status_parts)
