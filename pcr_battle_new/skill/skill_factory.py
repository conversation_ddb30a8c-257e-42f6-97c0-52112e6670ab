"""
技能工厂

负责技能的创建、执行和管理
"""

from typing import List, Optional, Dict, Any
import logging

from ..core.component_system import Entity
from ..core.event_system import Event, EventType, event_system
from ..character.components import AttributeComponent, SkillComponent
from ..character.character_data import SkillData, AttributeType
from .effect_strategies import get_effect_strategy
from .triggers import create_trigger_from_string

logger = logging.getLogger(__name__)


class SkillExecutionResult:
    """技能执行结果"""
    
    def __init__(self):
        self.success = False
        self.messages: List[str] = []
        self.effects: List[Dict[str, Any]] = []
        self.targets_affected: List[Entity] = []
        self.error_message = ""
    
    def add_message(self, message: str):
        """添加消息"""
        self.messages.append(message)
    
    def add_effect(self, effect: Dict[str, Any]):
        """添加效果"""
        self.effects.append(effect)
    
    def add_target(self, target: Entity):
        """添加受影响的目标"""
        if target not in self.targets_affected:
            self.targets_affected.append(target)


class SkillFactory:
    """技能工厂"""
    
    @staticmethod
    def execute_skill(caster: Entity, skill_id: str, all_entities: List[Entity], 
                     selected_target: Optional[Entity] = None) -> SkillExecutionResult:
        """执行技能"""
        result = SkillExecutionResult()
        
        # 获取技能组件
        skill_component = caster.get_component(SkillComponent)
        if not skill_component:
            result.error_message = "施法者缺少技能组件"
            return result
        
        # 获取技能数据
        skill = skill_component.get_skill(skill_id)
        if not skill:
            result.error_message = f"技能不存在: {skill_id}"
            return result
        
        # 检查TP是否足够
        attr_component = caster.get_component(AttributeComponent)
        if not attr_component:
            result.error_message = "施法者缺少属性组件"
            return result
        
        current_tp = attr_component.get_attribute(AttributeType.CURRENT_TP)
        if not skill_component.can_use_skill(skill_id, current_tp):
            result.error_message = "TP不足或技能冷却中"
            return result
        
        # 消耗TP
        attr_component.modify_attribute(AttributeType.CURRENT_TP, -skill.tp_cost)
        
        # 获取目标
        trigger = create_trigger_from_string(skill.trigger_type)
        targets = trigger.get_targets(caster, all_entities, selected_target)
        
        if not targets:
            result.error_message = "没有有效目标"
            # 返还TP
            attr_component.modify_attribute(AttributeType.CURRENT_TP, skill.tp_cost)
            return result
        
        # 发布技能使用事件
        event = Event(
            type=EventType.SKILL_USED,
            source=caster,
            data={
                "skill_id": skill_id,
                "skill_name": skill.name,
                "targets": targets,
                "tp_cost": skill.tp_cost
            }
        )
        event_system.emit(event)
        
        result.add_message(f"使用了技能: {skill.name}")
        
        # 执行技能效果
        success = SkillFactory._execute_skill_effects(caster, targets, skill, result)
        
        if success:
            result.success = True
            # 设置技能冷却（如果需要）
            # skill_component.set_cooldown(skill_id, 1.0)  # 1秒冷却
        else:
            # 技能执行失败，返还TP
            attr_component.modify_attribute(AttributeType.CURRENT_TP, skill.tp_cost)
        
        return result
    
    @staticmethod
    def _execute_skill_effects(caster: Entity, targets: List[Entity], 
                              skill: SkillData, result: SkillExecutionResult) -> bool:
        """执行技能效果"""
        try:
            # 先执行被动技能效果
            if skill.passive_skills:
                for passive_skill_id in skill.passive_skills:
                    skill_component = caster.get_component(SkillComponent)
                    passive_skill = skill_component.get_skill(passive_skill_id)
                    if passive_skill:
                        SkillFactory._apply_skill_effects(caster, targets, passive_skill.effects, result)
            
            # 执行主要技能效果
            SkillFactory._apply_skill_effects(caster, targets, skill.effects, result)
            
            return True
        except Exception as e:
            logger.error(f"Error executing skill effects: {e}")
            result.error_message = f"技能执行出错: {str(e)}"
            return False
    
    @staticmethod
    def _apply_skill_effects(caster: Entity, targets: List[Entity], 
                           effects: Dict[str, Any], result: SkillExecutionResult):
        """应用技能效果"""
        for effect_type, effect_data in effects.items():
            strategy = get_effect_strategy(effect_type)
            if not strategy:
                logger.warning(f"Unknown effect type: {effect_type}")
                continue
            
            # 对每个目标应用效果
            for target in targets:
                try:
                    effect_result = strategy.apply(caster, target, effect_data, {})
                    
                    if effect_result.get("success", False):
                        result.add_target(target)
                        result.add_effect(effect_result)
                        
                        if "message" in effect_result:
                            target_name = getattr(target, 'character_data', None)
                            target_name = target_name.name if target_name else "目标"
                            result.add_message(f"{target_name}: {effect_result['message']}")
                    else:
                        logger.warning(f"Effect failed: {effect_result.get('message', 'Unknown error')}")
                        
                except Exception as e:
                    logger.error(f"Error applying effect {effect_type}: {e}")
    
    @staticmethod
    def get_skill_info(entity: Entity, skill_id: str) -> Optional[Dict[str, Any]]:
        """获取技能信息"""
        skill_component = entity.get_component(SkillComponent)
        if not skill_component:
            return None
        
        skill = skill_component.get_skill(skill_id)
        if not skill:
            return None
        
        attr_component = entity.get_component(AttributeComponent)
        current_tp = attr_component.get_attribute(AttributeType.CURRENT_TP) if attr_component else 0
        
        return {
            "id": skill.id,
            "name": skill.name,
            "description": skill.description,
            "tp_cost": skill.tp_cost,
            "trigger_type": skill.trigger_type,
            "can_use": skill_component.can_use_skill(skill_id, current_tp),
            "effects": skill.effects
        }
    
    @staticmethod
    def get_all_skills_info(entity: Entity) -> Dict[str, Any]:
        """获取所有技能信息"""
        skill_component = entity.get_component(SkillComponent)
        if not skill_component:
            return {"active_skills": [], "passive_skills": []}
        
        active_skills = []
        for skill in skill_component.active_skills:
            skill_info = SkillFactory.get_skill_info(entity, skill.id)
            if skill_info:
                active_skills.append(skill_info)
        
        passive_skills = []
        for skill in skill_component.passive_skills:
            skill_info = SkillFactory.get_skill_info(entity, skill.id)
            if skill_info:
                passive_skills.append(skill_info)
        
        return {
            "active_skills": active_skills,
            "passive_skills": passive_skills
        }


# 便利函数
def execute_skill(caster: Entity, skill_id: str, all_entities: List[Entity], 
                 selected_target: Optional[Entity] = None) -> SkillExecutionResult:
    """执行技能的便利函数"""
    return SkillFactory.execute_skill(caster, skill_id, all_entities, selected_target)


def get_skill_info(entity: Entity, skill_id: str) -> Optional[Dict[str, Any]]:
    """获取技能信息的便利函数"""
    return SkillFactory.get_skill_info(entity, skill_id)
