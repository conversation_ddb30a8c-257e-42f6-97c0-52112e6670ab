"""
技能触发器

定义技能的触发条件和目标选择逻辑
"""

from typing import List, Optional, Dict, Any
from enum import Enum
from abc import ABC, abstractmethod

from ..core.component_system import Entity


class TriggerType(Enum):
    """触发类型枚举"""
    SELECT = "select"                    # 选择目标
    SELECT_EXCEPT_ME = "select_except_me"  # 选择目标（除了自己）
    SELF = "me"                         # 自己
    ALL = "all"                         # 所有人
    ALL_EXCEPT_ME = "all_except_me"     # 所有人（除了自己）
    NEAREST = "nearest"                 # 最近的目标
    RANDOM = "random"                   # 随机目标
    LOWEST_HP = "lowest_hp"             # 生命值最低的目标
    HIGHEST_HP = "highest_hp"           # 生命值最高的目标


class SkillTrigger(ABC):
    """技能触发器基类"""
    
    @abstractmethod
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        pass
    
    @abstractmethod
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        pass


class SelectTrigger(SkillTrigger):
    """选择目标触发器"""
    
    def __init__(self, exclude_self: bool = False):
        self.exclude_self = exclude_self
    
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        if not selected_target:
            return []
        
        if self.exclude_self and selected_target == caster:
            return []
        
        return [selected_target]
    
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        # 检查目标是否存活
        from ..character.components import AttributeComponent
        from ..character.character_data import AttributeType
        
        attr_component = target.get_component(AttributeComponent)
        if not attr_component:
            return False
        
        current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
        if current_health <= 0:
            return False
        
        # 检查是否排除自己
        if self.exclude_self and target == caster:
            return False
        
        return True


class SelfTrigger(SkillTrigger):
    """自身触发器"""
    
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        return [caster]
    
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        return target == caster


class AllTrigger(SkillTrigger):
    """全体触发器"""
    
    def __init__(self, exclude_self: bool = False):
        self.exclude_self = exclude_self
    
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        targets = []
        
        for entity in all_entities:
            if self.exclude_self and entity == caster:
                continue
            
            # 只选择存活的目标
            if self.validate_target(caster, entity, {}):
                targets.append(entity)
        
        return targets
    
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        from ..character.components import AttributeComponent
        from ..character.character_data import AttributeType
        
        attr_component = target.get_component(AttributeComponent)
        if not attr_component:
            return False
        
        current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
        return current_health > 0


class NearestTrigger(SkillTrigger):
    """最近目标触发器"""
    
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        # 这里需要游戏逻辑层提供位置信息
        # 暂时返回第一个有效目标
        for entity in all_entities:
            if entity != caster and self.validate_target(caster, entity, {}):
                return [entity]
        return []
    
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        from ..character.components import AttributeComponent
        from ..character.character_data import AttributeType
        
        if target == caster:
            return False
        
        attr_component = target.get_component(AttributeComponent)
        if not attr_component:
            return False
        
        current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
        return current_health > 0


class RandomTrigger(SkillTrigger):
    """随机目标触发器"""
    
    def __init__(self, count: int = 1, exclude_self: bool = True):
        self.count = count
        self.exclude_self = exclude_self
    
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        import random
        
        valid_targets = []
        for entity in all_entities:
            if self.exclude_self and entity == caster:
                continue
            if self.validate_target(caster, entity, {}):
                valid_targets.append(entity)
        
        if not valid_targets:
            return []
        
        # 随机选择目标
        selected_count = min(self.count, len(valid_targets))
        return random.sample(valid_targets, selected_count)
    
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        from ..character.components import AttributeComponent
        from ..character.character_data import AttributeType
        
        attr_component = target.get_component(AttributeComponent)
        if not attr_component:
            return False
        
        current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
        return current_health > 0


class HPBasedTrigger(SkillTrigger):
    """基于生命值的触发器"""
    
    def __init__(self, target_lowest: bool = True, exclude_self: bool = True):
        self.target_lowest = target_lowest  # True选择最低血量，False选择最高血量
        self.exclude_self = exclude_self
    
    def get_targets(self, caster: Entity, all_entities: List[Entity], 
                   selected_target: Optional[Entity] = None) -> List[Entity]:
        """获取技能目标"""
        from ..character.components import AttributeComponent
        from ..character.character_data import AttributeType
        
        valid_targets = []
        for entity in all_entities:
            if self.exclude_self and entity == caster:
                continue
            if self.validate_target(caster, entity, {}):
                attr_component = entity.get_component(AttributeComponent)
                if attr_component:
                    hp = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
                    valid_targets.append((entity, hp))
        
        if not valid_targets:
            return []
        
        # 根据血量排序
        valid_targets.sort(key=lambda x: x[1], reverse=not self.target_lowest)
        return [valid_targets[0][0]]
    
    def validate_target(self, caster: Entity, target: Entity, 
                       context: Dict[str, Any]) -> bool:
        """验证目标是否有效"""
        from ..character.components import AttributeComponent
        from ..character.character_data import AttributeType
        
        attr_component = target.get_component(AttributeComponent)
        if not attr_component:
            return False
        
        current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
        return current_health > 0


# 触发器工厂
def create_trigger(trigger_type: TriggerType) -> SkillTrigger:
    """创建触发器"""
    if trigger_type == TriggerType.SELECT:
        return SelectTrigger(exclude_self=False)
    elif trigger_type == TriggerType.SELECT_EXCEPT_ME:
        return SelectTrigger(exclude_self=True)
    elif trigger_type == TriggerType.SELF:
        return SelfTrigger()
    elif trigger_type == TriggerType.ALL:
        return AllTrigger(exclude_self=False)
    elif trigger_type == TriggerType.ALL_EXCEPT_ME:
        return AllTrigger(exclude_self=True)
    elif trigger_type == TriggerType.NEAREST:
        return NearestTrigger()
    elif trigger_type == TriggerType.RANDOM:
        return RandomTrigger()
    elif trigger_type == TriggerType.LOWEST_HP:
        return HPBasedTrigger(target_lowest=True)
    elif trigger_type == TriggerType.HIGHEST_HP:
        return HPBasedTrigger(target_lowest=False)
    else:
        raise ValueError(f"Unknown trigger type: {trigger_type}")


def create_trigger_from_string(trigger_str: str) -> SkillTrigger:
    """从字符串创建触发器"""
    try:
        trigger_type = TriggerType(trigger_str)
        return create_trigger(trigger_type)
    except ValueError:
        # 兼容旧版本的触发器字符串
        if trigger_str == "select_except_me":
            return SelectTrigger(exclude_self=True)
        elif trigger_str == "select":
            return SelectTrigger(exclude_self=False)
        elif trigger_str == "me":
            return SelfTrigger()
        elif trigger_str == "all":
            return AllTrigger(exclude_self=False)
        elif trigger_str == "all_except_me":
            return AllTrigger(exclude_self=True)
        elif trigger_str == "near":
            return NearestTrigger()
        else:
            raise ValueError(f"Unknown trigger string: {trigger_str}")
