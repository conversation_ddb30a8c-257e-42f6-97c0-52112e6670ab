"""
技能效果策略

使用策略模式实现各种技能效果
"""

from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod
import random
import math
import logging

from ..core.component_system import Entity
from ..core.event_system import Event, EventType, event_system
from ..character.components import AttributeComponent, BuffComponent, BuffData
from ..character.character_data import AttributeType

logger = logging.getLogger(__name__)


class EffectStrategy(ABC):
    """技能效果策略基类"""
    
    @abstractmethod
    def apply(self, caster: Entity, target: Entity, effect_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用技能效果"""
        pass


class DamageEffect(EffectStrategy):
    """伤害效果"""
    
    def apply(self, caster: Entity, target: Entity, effect_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用伤害效果"""
        caster_attr = caster.get_component(AttributeComponent)
        target_attr = target.get_component(AttributeComponent)
        
        if not caster_attr or not target_attr:
            return {"success": False, "message": "Missing attribute components"}
        
        # 计算基础伤害
        base_damage = effect_data.get("base", 0)
        attack_ratio = effect_data.get("attack_ratio", 0)
        is_true_damage = effect_data.get("true_damage", False)
        
        caster_attack = caster_attr.get_attribute(AttributeType.ATTACK)
        damage = base_damage + (caster_attack * attack_ratio)
        
        # 暴击计算
        crit_rate = caster_attr.get_attribute(AttributeType.CRIT_RATE)
        is_crit = random.random() * 100 < crit_rate
        
        if is_crit:
            crit_damage = caster_attr.get_attribute(AttributeType.CRIT_DAMAGE)
            damage *= crit_damage
        
        # 防御计算（非真实伤害）
        if not is_true_damage:
            defense = target_attr.get_attribute(AttributeType.DEFENSE)
            damage = self._calculate_damage_reduction(damage, defense)
        
        # 应用伤害
        final_damage = math.floor(damage)
        target_attr.modify_attribute(AttributeType.CURRENT_HEALTH, -final_damage)
        
        # 受伤回复TP
        if final_damage > 0:
            max_health = target_attr.get_attribute(AttributeType.MAX_HEALTH)
            tp_gain = math.floor(final_damage / max_health * 100 / 2)
            target_attr.modify_attribute(AttributeType.CURRENT_TP, tp_gain)
        
        # 发布伤害事件
        event = Event(
            type=EventType.DAMAGE_DEALT,
            source=caster,
            data={
                "target": target,
                "damage": final_damage,
                "is_crit": is_crit,
                "is_true_damage": is_true_damage
            }
        )
        event_system.emit(event)
        
        result = {
            "success": True,
            "damage": final_damage,
            "is_crit": is_crit,
            "message": f"造成了{final_damage}点伤害" + ("（暴击！）" if is_crit else "")
        }
        
        # 检查目标是否被击败
        if target_attr.get_attribute(AttributeType.CURRENT_HEALTH) <= 0:
            event = Event(
                type=EventType.PLAYER_DEFEATED,
                source=target,
                data={
                    "defeated_by": caster,
                    "damage": final_damage
                }
            )
            event_system.emit(event)
            result["target_defeated"] = True
        
        return result
    
    def _calculate_damage_reduction(self, damage: float, defense: float) -> float:
        """计算防御减伤"""
        # 使用原版的防御计算公式
        percent = 0.0
        if defense <= 100:
            percent = defense * 0.0015
        else:
            if defense <= 500:
                percent = 100 * 0.0015 + (defense - 100) * 0.0007
            elif 500 < defense <= 1000:
                percent = 100 * 0.0015 + 400 * 0.0007 + (defense - 500) * 0.0005
            else:
                percent = 100 * 0.0015 + 400 * 0.0007 + 500 * 0.0005
        
        return damage * (1 - percent)


class HealEffect(EffectStrategy):
    """治疗效果"""
    
    def apply(self, caster: Entity, target: Entity, effect_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用治疗效果"""
        caster_attr = caster.get_component(AttributeComponent)
        target_attr = target.get_component(AttributeComponent)
        
        if not target_attr:
            return {"success": False, "message": "Target missing attribute component"}
        
        # 计算治疗量
        base_heal = effect_data.get("base", 0)
        attack_ratio = effect_data.get("attack_ratio", 0)
        
        heal_amount = base_heal
        if caster_attr and attack_ratio > 0:
            caster_attack = caster_attr.get_attribute(AttributeType.ATTACK)
            heal_amount += caster_attack * attack_ratio
        
        # 应用治疗
        final_heal = math.floor(heal_amount)
        target_attr.modify_attribute(AttributeType.CURRENT_HEALTH, final_heal)
        
        return {
            "success": True,
            "heal": final_heal,
            "message": f"恢复了{final_heal}点生命值"
        }


class BuffEffect(EffectStrategy):
    """Buff效果"""
    
    def apply(self, caster: Entity, target: Entity, effect_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用Buff效果"""
        target_buff = target.get_component(BuffComponent)
        
        if not target_buff:
            return {"success": False, "message": "Target missing buff component"}
        
        # 创建Buff
        buff_data = BuffData(
            id=effect_data.get("id", "unknown_buff"),
            name=effect_data.get("name", "未知Buff"),
            description=effect_data.get("description", ""),
            effect_type=effect_data.get("effect_type", "attribute"),
            value=effect_data.get("value", 0),
            duration=effect_data.get("duration", 1),
            remaining_turns=effect_data.get("duration", 1),
            max_stacks=effect_data.get("max_stacks", 1)
        )
        
        # 添加Buff
        target_buff.add_buff(buff_data)
        
        # 发布Buff事件
        event = Event(
            type=EventType.BUFF_APPLIED,
            source=caster,
            data={
                "target": target,
                "buff": buff_data
            }
        )
        event_system.emit(event)
        
        return {
            "success": True,
            "buff_id": buff_data.id,
            "message": f"获得了{buff_data.name}效果"
        }


class AttributeModifyEffect(EffectStrategy):
    """属性修改效果"""
    
    def apply(self, caster: Entity, target: Entity, effect_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用属性修改效果"""
        target_attr = target.get_component(AttributeComponent)
        
        if not target_attr:
            return {"success": False, "message": "Target missing attribute component"}
        
        # 获取修改参数
        attr_type_str = effect_data.get("attribute", "")
        base_value = effect_data.get("base", 0)
        ratio = effect_data.get("ratio", 0)
        
        try:
            attr_type = AttributeType(attr_type_str)
        except ValueError:
            return {"success": False, "message": f"Invalid attribute type: {attr_type_str}"}
        
        # 计算修改值
        modify_value = base_value
        if ratio > 0:
            current_value = target_attr.get_attribute(attr_type)
            modify_value += current_value * ratio
        
        # 应用修改
        final_value = math.floor(modify_value)
        target_attr.modify_attribute(attr_type, final_value)
        
        action = "增加" if final_value > 0 else "减少"
        attr_name = self._get_attribute_name(attr_type)
        
        return {
            "success": True,
            "attribute": attr_type.value,
            "value": final_value,
            "message": f"{action}了{abs(final_value)}点{attr_name}"
        }
    
    def _get_attribute_name(self, attr_type: AttributeType) -> str:
        """获取属性中文名称"""
        name_map = {
            AttributeType.MAX_HEALTH: "最大生命值",
            AttributeType.CURRENT_HEALTH: "生命值",
            AttributeType.ATTACK: "攻击力",
            AttributeType.DEFENSE: "防御力",
            AttributeType.DISTANCE: "攻击距离",
            AttributeType.CRIT_RATE: "暴击率",
            AttributeType.CRIT_DAMAGE: "暴击伤害",
            AttributeType.CURRENT_TP: "TP",
            AttributeType.MAX_TP: "最大TP",
        }
        return name_map.get(attr_type, attr_type.value)


class MovementEffect(EffectStrategy):
    """移动效果"""
    
    def apply(self, caster: Entity, target: Entity, effect_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """应用移动效果"""
        # 移动效果需要游戏逻辑层的支持，这里只是标记
        steps = effect_data.get("steps", 0)
        direction = effect_data.get("direction", "forward")  # forward, backward, random
        
        # 发布移动事件，由游戏逻辑层处理
        event = Event(
            type=EventType.PLAYER_MOVED,
            source=caster,
            data={
                "target": target,
                "steps": steps,
                "direction": direction,
                "trigger": "skill"
            }
        )
        event_system.emit(event)
        
        return {
            "success": True,
            "steps": steps,
            "direction": direction,
            "message": f"移动了{abs(steps)}步"
        }


# 效果策略注册表
EFFECT_STRATEGIES: Dict[str, EffectStrategy] = {
    "damage": DamageEffect(),
    "heal": HealEffect(),
    "buff": BuffEffect(),
    "attribute": AttributeModifyEffect(),
    "movement": MovementEffect(),
}


def get_effect_strategy(effect_type: str) -> Optional[EffectStrategy]:
    """获取效果策略"""
    return EFFECT_STRATEGIES.get(effect_type)


def register_effect_strategy(effect_type: str, strategy: EffectStrategy):
    """注册效果策略"""
    EFFECT_STRATEGIES[effect_type] = strategy
