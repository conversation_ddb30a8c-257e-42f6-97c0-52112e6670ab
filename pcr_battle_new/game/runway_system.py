"""
跑道系统

管理跑道事件和玩家移动
"""

from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import random
import logging

from ..core.event_system import Event, EventType, event_system
from ..character.components import AttributeComponent
from ..character.character_data import AttributeType
from .room_manager import GameRoom

logger = logging.getLogger(__name__)


class RunwayEventType(Enum):
    """跑道事件类型"""
    NONE = "none"                # 无事件
    HEALTH = "health"            # 生命值事件
    ATTACK = "attack"            # 攻击力事件
    DEFENSE = "defense"          # 防御力事件
    TP = "tp"                   # TP事件
    MOVE = "move"               # 移动事件
    BUFF = "buff"               # Buff事件
    TRAP = "trap"               # 陷阱事件


@dataclass
class RunwayEvent:
    """跑道事件"""
    event_type: RunwayEventType
    name: str
    description: str
    effect_range: tuple  # (最小值, 最大值)
    probability: float = 1.0  # 触发概率
    effect_handler: Optional[Callable] = None  # 自定义效果处理器


class RunwaySystem:
    """跑道系统"""
    
    def __init__(self, room: GameRoom):
        self.room = room
        self.events: Dict[int, RunwayEvent] = {}
        self._initialize_runway_events()
    
    def trigger_event(self, user_id: str, position: int) -> List[str]:
        """触发跑道事件"""
        if position not in self.events:
            return ["什么也没发生"]
        
        event = self.events[position]
        
        # 检查触发概率
        if random.random() > event.probability:
            return ["什么也没发生"]
        
        if user_id not in self.room.players:
            return ["玩家不存在"]
        
        player_info = self.room.players[user_id]
        if not player_info.character_entity:
            return ["角色不存在"]
        
        messages = []
        
        # 使用自定义处理器或默认处理器
        if event.effect_handler:
            result = event.effect_handler(player_info.character_entity, event)
            messages.extend(result)
        else:
            result = self._handle_default_event(player_info.character_entity, event)
            messages.extend(result)
        
        # 发布跑道事件
        event_data = Event(
            type=EventType.RUNWAY_EVENT_TRIGGERED,
            source=self.room,
            data={
                "room_id": self.room.room_id,
                "user_id": user_id,
                "position": position,
                "event_type": event.event_type.value,
                "event_name": event.name,
                "messages": messages
            }
        )
        event_system.emit(event_data)
        
        logger.info(f"Runway event triggered for player {user_id} at position {position}: {event.name}")
        return messages
    
    def set_event(self, position: int, event: RunwayEvent):
        """设置跑道事件"""
        if 0 <= position < self.room.runway_size:
            self.events[position] = event
    
    def remove_event(self, position: int):
        """移除跑道事件"""
        self.events.pop(position, None)
    
    def get_event(self, position: int) -> Optional[RunwayEvent]:
        """获取跑道事件"""
        return self.events.get(position)
    
    def randomize_events(self):
        """随机化跑道事件"""
        self.events.clear()
        
        # 预定义的事件类型
        event_types = [
            RunwayEventType.NONE,
            RunwayEventType.HEALTH,
            RunwayEventType.ATTACK,
            RunwayEventType.DEFENSE,
            RunwayEventType.TP,
            RunwayEventType.MOVE,
        ]
        
        # 为每个位置随机分配事件
        for position in range(self.room.runway_size):
            event_type = random.choice(event_types)
            event = self._create_event_by_type(event_type)
            self.events[position] = event
    
    def _initialize_runway_events(self):
        """初始化跑道事件"""
        # 创建默认的跑道事件分布
        self.randomize_events()
    
    def _handle_default_event(self, character_entity, event: RunwayEvent) -> List[str]:
        """处理默认事件"""
        attr_component = character_entity.get_component(AttributeComponent)
        if not attr_component:
            return ["角色属性组件缺失"]
        
        messages = []
        
        if event.event_type == RunwayEventType.NONE:
            messages.append("什么也没发生")
        
        elif event.event_type == RunwayEventType.HEALTH:
            value = random.randint(*event.effect_range)
            attr_component.modify_attribute(AttributeType.CURRENT_HEALTH, value)
            action = "恢复" if value > 0 else "失去"
            messages.append(f"{action}了{abs(value)}点生命值")
        
        elif event.event_type == RunwayEventType.ATTACK:
            value = random.randint(*event.effect_range)
            attr_component.modify_attribute(AttributeType.ATTACK, value)
            action = "增加" if value > 0 else "减少"
            messages.append(f"{action}了{abs(value)}点攻击力")
        
        elif event.event_type == RunwayEventType.DEFENSE:
            value = random.randint(*event.effect_range)
            attr_component.modify_attribute(AttributeType.DEFENSE, value)
            action = "增加" if value > 0 else "减少"
            messages.append(f"{action}了{abs(value)}点防御力")
        
        elif event.event_type == RunwayEventType.TP:
            value = random.randint(*event.effect_range)
            attr_component.modify_attribute(AttributeType.CURRENT_TP, value)
            action = "恢复" if value > 0 else "失去"
            messages.append(f"{action}了{abs(value)}点TP")
        
        elif event.event_type == RunwayEventType.MOVE:
            value = random.randint(*event.effect_range)
            if value != 0:
                # 移动玩家
                player_id = getattr(character_entity, 'player_id', None)
                if player_id:
                    self.room.move_player(player_id, value)
                    direction = "前进" if value > 0 else "后退"
                    messages.append(f"{direction}了{abs(value)}步")
                    
                    # 递归触发新位置的事件
                    new_position = self.room.players[player_id].position
                    additional_messages = self.trigger_event(player_id, new_position)
                    messages.extend(additional_messages)
            else:
                messages.append("位置没有变化")
        
        return messages
    
    def _create_event_by_type(self, event_type: RunwayEventType) -> RunwayEvent:
        """根据类型创建事件"""
        if event_type == RunwayEventType.NONE:
            return RunwayEvent(
                event_type=event_type,
                name="平静",
                description="什么也没发生",
                effect_range=(0, 0)
            )
        
        elif event_type == RunwayEventType.HEALTH:
            return RunwayEvent(
                event_type=event_type,
                name="生命之泉",
                description="影响生命值",
                effect_range=(-50, 100)
            )
        
        elif event_type == RunwayEventType.ATTACK:
            return RunwayEvent(
                event_type=event_type,
                name="力量祝福",
                description="影响攻击力",
                effect_range=(-20, 30)
            )
        
        elif event_type == RunwayEventType.DEFENSE:
            return RunwayEvent(
                event_type=event_type,
                name="护盾符文",
                description="影响防御力",
                effect_range=(-15, 25)
            )
        
        elif event_type == RunwayEventType.TP:
            return RunwayEvent(
                event_type=event_type,
                name="魔力涌泉",
                description="影响TP值",
                effect_range=(-20, 30)
            )
        
        elif event_type == RunwayEventType.MOVE:
            return RunwayEvent(
                event_type=event_type,
                name="传送门",
                description="强制移动",
                effect_range=(-3, 3)
            )
        
        else:
            return RunwayEvent(
                event_type=RunwayEventType.NONE,
                name="未知事件",
                description="未知的事件",
                effect_range=(0, 0)
            )
    
    def get_runway_display(self) -> Dict[int, str]:
        """获取跑道显示信息"""
        display = {}
        for position in range(self.room.runway_size):
            event = self.events.get(position)
            if event:
                display[position] = event.name
            else:
                display[position] = "空地"
        return display
    
    def get_players_positions(self) -> Dict[str, int]:
        """获取所有玩家位置"""
        positions = {}
        for user_id, player_info in self.room.players.items():
            positions[user_id] = player_info.position
        return positions
