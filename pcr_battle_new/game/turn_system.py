"""
回合系统

管理游戏的回合制逻辑，包括回合切换、行动顺序等
"""

from typing import Optional, List, Dict, Any
import random
import logging

from ..core.event_system import Event, EventType, event_system
from ..core.state_manager import PlayerState, state_manager
from ..character.components import AttributeComponent, BuffComponent
from ..character.character_data import AttributeType
from .room_manager import GameRoom

logger = logging.getLogger(__name__)


class TurnSystem:
    """回合系统"""
    
    def __init__(self, room: GameRoom):
        self.room = room
        self.turn_timeout = 30  # 回合超时时间（秒）
        self.dice_sides = 6  # 色子面数
        
    def start_turn(self, user_id: str) -> bool:
        """开始玩家回合"""
        if user_id not in self.room.players:
            return False
        
        player_info = self.room.players[user_id]
        if not player_info.character_entity:
            return False
        
        # 更新玩家状态
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if player_state_machine:
            player_state_machine.transition_to(PlayerState.TURN_DICE)
        
        # 处理回合开始时的效果
        self._process_turn_start_effects(player_info.character_entity)
        
        # 发布回合开始事件
        event = Event(
            type=EventType.TURN_STARTED,
            source=self.room,
            data={
                "room_id": self.room.room_id,
                "user_id": user_id,
                "turn_count": self.room.turn_count
            }
        )
        event_system.emit(event)
        
        logger.info(f"Turn started for player {user_id} in room {self.room.room_id}")
        return True
    
    def end_turn(self, user_id: str) -> bool:
        """结束玩家回合"""
        if user_id not in self.room.players:
            return False
        
        player_info = self.room.players[user_id]
        if not player_info.character_entity:
            return False
        
        # 处理回合结束时的效果
        self._process_turn_end_effects(player_info.character_entity)
        
        # 更新玩家状态
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if player_state_machine:
            player_state_machine.transition_to(PlayerState.WAITING)
        
        # 发布回合结束事件
        event = Event(
            type=EventType.TURN_ENDED,
            source=self.room,
            data={
                "room_id": self.room.room_id,
                "user_id": user_id,
                "turn_count": self.room.turn_count
            }
        )
        event_system.emit(event)
        
        logger.info(f"Turn ended for player {user_id} in room {self.room.room_id}")
        return True
    
    def next_turn(self) -> Optional[str]:
        """切换到下一个玩家的回合"""
        alive_players = self.room.get_alive_players()
        if len(alive_players) <= 1:
            # 游戏结束
            winner = alive_players[0] if alive_players else None
            self.room.end_game(winner)
            return None
        
        # 寻找下一个存活的玩家
        attempts = 0
        while attempts < len(self.room.player_order):
            self.room.current_turn_index = (self.room.current_turn_index + 1) % len(self.room.player_order)
            next_player = self.room.player_order[self.room.current_turn_index]
            
            if next_player in alive_players:
                self.room.turn_count += 1
                
                # 检查是否完成一轮
                if self.room.current_turn_index == 0:
                    self.room.round_count += 1
                    self._process_round_effects()
                
                self.start_turn(next_player)
                return next_player
            
            attempts += 1
        
        # 没有找到存活玩家，游戏结束
        self.room.end_game()
        return None
    
    def throw_dice(self, user_id: str) -> Optional[int]:
        """丢色子"""
        if user_id != self.room.get_current_player():
            return None
        
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if not player_state_machine or player_state_machine.current_state != PlayerState.TURN_DICE:
            return None
        
        # 投掷色子
        dice_result = random.randint(1, self.dice_sides)
        
        # 移动玩家
        self.room.move_player(user_id, dice_result)
        
        # 更新玩家状态
        player_state_machine.transition_to(PlayerState.TURN_SKILL)
        
        # 发布色子事件
        event = Event(
            type=EventType.DICE_THROWN,
            source=self.room,
            data={
                "room_id": self.room.room_id,
                "user_id": user_id,
                "dice_result": dice_result,
                "new_position": self.room.players[user_id].position
            }
        )
        event_system.emit(event)
        
        logger.info(f"Player {user_id} threw dice: {dice_result}")
        return dice_result
    
    def _process_turn_start_effects(self, character_entity):
        """处理回合开始时的效果"""
        # 恢复TP
        attr_component = character_entity.get_component(AttributeComponent)
        if attr_component:
            tp_gain = 10  # 每回合恢复10点TP
            attr_component.modify_attribute(AttributeType.CURRENT_TP, tp_gain)
        
        # 处理Buff效果
        buff_component = character_entity.get_component(BuffComponent)
        if buff_component:
            # 触发回合开始时的Buff效果
            for buff in buff_component.buffs.values():
                if buff.effect_type == "turn_start":
                    self._apply_buff_effect(character_entity, buff)
    
    def _process_turn_end_effects(self, character_entity):
        """处理回合结束时的效果"""
        # 处理Buff持续时间
        buff_component = character_entity.get_component(BuffComponent)
        if buff_component:
            expired_buffs = buff_component.process_turn_end()
            
            # 处理回合结束时的Buff效果
            for buff in buff_component.buffs.values():
                if buff.effect_type == "turn_end":
                    self._apply_buff_effect(character_entity, buff)
    
    def _process_round_effects(self):
        """处理轮次效果"""
        # 每轮增加攻击力和攻击距离
        alive_players = self.room.get_alive_players()
        for user_id in alive_players:
            player_info = self.room.players[user_id]
            if player_info.character_entity:
                attr_component = player_info.character_entity.get_component(AttributeComponent)
                if attr_component:
                    attr_component.modify_attribute(AttributeType.ATTACK, 10)
                    attr_component.modify_attribute(AttributeType.DISTANCE, 2)
        
        logger.info(f"Round {self.room.round_count} effects applied in room {self.room.room_id}")
    
    def _apply_buff_effect(self, character_entity, buff):
        """应用Buff效果"""
        # 这里可以根据Buff类型应用不同的效果
        # 暂时简化处理
        pass
