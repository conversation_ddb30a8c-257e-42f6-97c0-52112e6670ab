"""
房间管理器

负责游戏房间的创建、管理和生命周期
"""

from typing import Dict, List, Optional, Set
from dataclasses import dataclass, field
import time
import logging

from ..core.event_system import Event, EventType, event_system
from ..core.state_manager import GameState, PlayerState, state_manager
from ..core.component_system import Entity
from ..character.character_factory import CharacterFactory

logger = logging.getLogger(__name__)


@dataclass
class PlayerInfo:
    """玩家信息"""
    user_id: str
    nickname: str
    character_entity: Optional[Entity] = None
    position: int = 0  # 在跑道上的位置
    is_ready: bool = False
    join_time: float = field(default_factory=time.time)


class GameRoom:
    """游戏房间"""
    
    def __init__(self, room_id: str, creator_id: str, max_players: int = 4):
        self.room_id = room_id
        self.creator_id = creator_id
        self.max_players = max_players
        self.create_time = time.time()
        
        # 玩家管理
        self.players: Dict[str, PlayerInfo] = {}
        self.player_order: List[str] = []  # 玩家行动顺序
        self.current_turn_index = 0
        
        # 游戏状态
        self.state_machine = state_manager.create_game_state_machine(room_id)
        self.winner: Optional[str] = None
        self.game_data: Dict[str, any] = {}
        
        # 跑道系统
        self.runway_size = 36
        self.runway_positions: Dict[int, Set[str]] = {i: set() for i in range(self.runway_size)}
        
        # 回合计数
        self.turn_count = 0
        self.round_count = 0
        
        logger.info(f"Created game room {room_id} by {creator_id}")
    
    def add_player(self, user_id: str, nickname: str) -> bool:
        """添加玩家"""
        if len(self.players) >= self.max_players:
            return False
        
        if user_id in self.players:
            return False
        
        if self.state_machine.current_state != GameState.WAITING:
            return False
        
        player_info = PlayerInfo(user_id=user_id, nickname=nickname)
        self.players[user_id] = player_info
        
        # 创建玩家状态机
        state_manager.create_player_state_machine(user_id)
        
        # 发布玩家加入事件
        event = Event(
            type=EventType.PLAYER_JOINED,
            source=self,
            data={
                "room_id": self.room_id,
                "user_id": user_id,
                "nickname": nickname,
                "player_count": len(self.players)
            }
        )
        event_system.emit(event)
        
        logger.info(f"Player {nickname}({user_id}) joined room {self.room_id}")
        return True
    
    def remove_player(self, user_id: str) -> bool:
        """移除玩家"""
        if user_id not in self.players:
            return False
        
        player_info = self.players[user_id]
        
        # 销毁角色实体
        if player_info.character_entity:
            CharacterFactory.destroy_character(player_info.character_entity)
        
        # 从跑道移除
        for position_set in self.runway_positions.values():
            position_set.discard(user_id)
        
        # 从玩家列表移除
        del self.players[user_id]
        
        # 从行动顺序移除
        if user_id in self.player_order:
            old_index = self.player_order.index(user_id)
            self.player_order.remove(user_id)
            
            # 调整当前回合索引
            if old_index <= self.current_turn_index and self.current_turn_index > 0:
                self.current_turn_index -= 1
        
        # 移除玩家状态机
        state_manager.remove_player_state_machine(user_id)
        
        # 发布玩家离开事件
        event = Event(
            type=EventType.PLAYER_LEFT,
            source=self,
            data={
                "room_id": self.room_id,
                "user_id": user_id,
                "player_count": len(self.players)
            }
        )
        event_system.emit(event)
        
        logger.info(f"Player {user_id} left room {self.room_id}")
        return True
    
    def select_character(self, user_id: str, character_id: str) -> bool:
        """选择角色"""
        if user_id not in self.players:
            return False
        
        if self.state_machine.current_state != GameState.CHARACTER_SELECT:
            return False
        
        player_info = self.players[user_id]
        
        # 检查角色是否已被选择
        for other_player in self.players.values():
            if (other_player.character_entity and 
                hasattr(other_player.character_entity, 'character_id') and
                other_player.character_entity.character_id == character_id):
                return False
        
        # 销毁旧角色
        if player_info.character_entity:
            CharacterFactory.destroy_character(player_info.character_entity)
        
        # 创建新角色
        character_entity = CharacterFactory.create_character(character_id, user_id)
        if not character_entity:
            return False
        
        player_info.character_entity = character_entity
        player_info.is_ready = True
        
        # 更新玩家状态
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if player_state_machine:
            player_state_machine.transition_to(PlayerState.READY)
        
        logger.info(f"Player {user_id} selected character {character_id}")
        return True
    
    def start_game(self) -> bool:
        """开始游戏"""
        if self.state_machine.current_state != GameState.CHARACTER_SELECT:
            return False
        
        if len(self.players) < 2:
            return False
        
        # 检查所有玩家是否都选择了角色
        for player_info in self.players.values():
            if not player_info.is_ready or not player_info.character_entity:
                return False
        
        # 初始化玩家位置和顺序
        self._initialize_game()
        
        # 转换游戏状态
        self.state_machine.transition_to(GameState.PLAYING)
        
        # 发布游戏开始事件
        event = Event(
            type=EventType.GAME_STARTED,
            source=self,
            data={
                "room_id": self.room_id,
                "players": list(self.players.keys()),
                "player_count": len(self.players)
            }
        )
        event_system.emit(event)
        
        logger.info(f"Game started in room {self.room_id}")
        return True
    
    def end_game(self, winner_id: Optional[str] = None) -> bool:
        """结束游戏"""
        if self.state_machine.current_state not in [GameState.PLAYING, GameState.PAUSED]:
            return False
        
        self.winner = winner_id
        self.state_machine.transition_to(GameState.ENDED)
        
        # 发布游戏结束事件
        event = Event(
            type=EventType.GAME_ENDED,
            source=self,
            data={
                "room_id": self.room_id,
                "winner": winner_id,
                "duration": time.time() - self.create_time
            }
        )
        event_system.emit(event)
        
        logger.info(f"Game ended in room {self.room_id}, winner: {winner_id}")
        return True
    
    def get_current_player(self) -> Optional[str]:
        """获取当前回合玩家"""
        if not self.player_order:
            return None
        
        if self.current_turn_index >= len(self.player_order):
            return None
        
        return self.player_order[self.current_turn_index]
    
    def get_alive_players(self) -> List[str]:
        """获取存活玩家列表"""
        alive_players = []
        for user_id, player_info in self.players.items():
            if player_info.character_entity:
                from ..character.components import AttributeComponent
                from ..character.character_data import AttributeType
                
                attr_component = player_info.character_entity.get_component(AttributeComponent)
                if attr_component:
                    current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
                    if current_health > 0:
                        alive_players.append(user_id)
        
        return alive_players
    
    def move_player(self, user_id: str, steps: int) -> bool:
        """移动玩家"""
        if user_id not in self.players:
            return False
        
        player_info = self.players[user_id]
        
        # 从当前位置移除
        self.runway_positions[player_info.position].discard(user_id)
        
        # 计算新位置
        new_position = (player_info.position + steps) % self.runway_size
        if new_position < 0:
            new_position += self.runway_size
        
        # 移动到新位置
        player_info.position = new_position
        self.runway_positions[new_position].add(user_id)
        
        return True
    
    def _initialize_game(self):
        """初始化游戏"""
        # 设置玩家顺序
        self.player_order = list(self.players.keys())
        
        # 设置初始位置
        for i, user_id in enumerate(self.player_order):
            position = i * (self.runway_size // len(self.players))
            self.players[user_id].position = position
            self.runway_positions[position].add(user_id)
        
        # 重置回合计数
        self.turn_count = 0
        self.round_count = 0
        self.current_turn_index = 0


class RoomManager:
    """房间管理器"""
    
    def __init__(self):
        self.rooms: Dict[str, GameRoom] = {}
        self.user_room_map: Dict[str, str] = {}  # 用户ID -> 房间ID映射
    
    def create_room(self, room_id: str, creator_id: str, max_players: int = 4) -> Optional[GameRoom]:
        """创建房间"""
        if room_id in self.rooms:
            return None
        
        if creator_id in self.user_room_map:
            return None
        
        room = GameRoom(room_id, creator_id, max_players)
        room.state_machine.transition_to(GameState.WAITING)
        
        self.rooms[room_id] = room
        
        # 创建者自动加入房间
        if room.add_player(creator_id, f"Player_{creator_id}"):
            self.user_room_map[creator_id] = room_id
        
        # 发布房间创建事件
        event = Event(
            type=EventType.GAME_CREATED,
            source=room,
            data={
                "room_id": room_id,
                "creator_id": creator_id,
                "max_players": max_players
            }
        )
        event_system.emit(event)
        
        return room
    
    def get_room(self, room_id: str) -> Optional[GameRoom]:
        """获取房间"""
        return self.rooms.get(room_id)
    
    def get_user_room(self, user_id: str) -> Optional[GameRoom]:
        """获取用户所在房间"""
        room_id = self.user_room_map.get(user_id)
        if room_id:
            return self.rooms.get(room_id)
        return None
    
    def join_room(self, room_id: str, user_id: str, nickname: str) -> bool:
        """加入房间"""
        room = self.get_room(room_id)
        if not room:
            return False
        
        if user_id in self.user_room_map:
            return False
        
        if room.add_player(user_id, nickname):
            self.user_room_map[user_id] = room_id
            return True
        
        return False
    
    def leave_room(self, user_id: str) -> bool:
        """离开房间"""
        room = self.get_user_room(user_id)
        if not room:
            return False
        
        room.remove_player(user_id)
        self.user_room_map.pop(user_id, None)
        
        # 如果房间为空，删除房间
        if not room.players:
            self.destroy_room(room.room_id)
        
        return True
    
    def destroy_room(self, room_id: str) -> bool:
        """销毁房间"""
        room = self.get_room(room_id)
        if not room:
            return False
        
        # 移除所有玩家
        for user_id in list(room.players.keys()):
            room.remove_player(user_id)
            self.user_room_map.pop(user_id, None)
        
        # 移除房间状态机
        state_manager.remove_game_state_machine(room_id)
        
        # 删除房间
        del self.rooms[room_id]
        
        logger.info(f"Destroyed room {room_id}")
        return True
    
    def cleanup_empty_rooms(self):
        """清理空房间"""
        empty_rooms = [room_id for room_id, room in self.rooms.items() if not room.players]
        for room_id in empty_rooms:
            self.destroy_room(room_id)


# 全局房间管理器实例
room_manager = RoomManager()
