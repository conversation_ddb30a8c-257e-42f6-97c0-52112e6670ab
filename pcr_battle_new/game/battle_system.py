"""
战斗系统

处理战斗相关的逻辑，包括技能使用、伤害计算等
"""

from typing import Optional, List, Dict, Any
import random
import logging

from ..core.event_system import Event, EventType, event_system
from ..core.state_manager import PlayerState, state_manager
from ..skill.skill_factory import SkillFactory, SkillExecutionResult
from ..character.components import AttributeComponent
from ..character.character_data import AttributeType
from .room_manager import GameRoom

logger = logging.getLogger(__name__)


class BattleSystem:
    """战斗系统"""
    
    def __init__(self, room: GameRoom):
        self.room = room
    
    def use_skill(self, user_id: str, skill_id: str, target_id: Optional[str] = None) -> Optional[SkillExecutionResult]:
        """使用技能"""
        # 验证玩家状态
        if not self._validate_skill_use(user_id):
            return None
        
        player_info = self.room.players[user_id]
        caster_entity = player_info.character_entity
        
        # 获取所有角色实体
        all_entities = []
        for player in self.room.players.values():
            if player.character_entity:
                all_entities.append(player.character_entity)
        
        # 获取目标实体
        target_entity = None
        if target_id and target_id in self.room.players:
            target_entity = self.room.players[target_id].character_entity
        
        # 执行技能
        result = SkillFactory.execute_skill(caster_entity, skill_id, all_entities, target_entity)
        
        if result.success:
            # 检查是否有玩家被击败
            self._check_defeated_players(result)
            
            # 更新玩家状态 - 技能使用后结束回合
            player_state_machine = state_manager.get_player_state_machine(user_id)
            if player_state_machine:
                player_state_machine.transition_to(PlayerState.WAITING)
        
        logger.info(f"Player {user_id} used skill {skill_id}, success: {result.success}")
        return result
    
    def calculate_distance(self, user_id1: str, user_id2: str) -> int:
        """计算两个玩家之间的距离"""
        if user_id1 not in self.room.players or user_id2 not in self.room.players:
            return float('inf')
        
        pos1 = self.room.players[user_id1].position
        pos2 = self.room.players[user_id2].position
        
        # 计算环形跑道上的最短距离
        direct_distance = abs(pos1 - pos2)
        wrap_distance = self.room.runway_size - direct_distance
        
        return min(direct_distance, wrap_distance)
    
    def can_attack(self, attacker_id: str, target_id: str) -> bool:
        """检查是否可以攻击目标"""
        if attacker_id not in self.room.players or target_id not in self.room.players:
            return False
        
        attacker_entity = self.room.players[attacker_id].character_entity
        target_entity = self.room.players[target_id].character_entity
        
        if not attacker_entity or not target_entity:
            return False
        
        # 检查目标是否存活
        target_attr = target_entity.get_component(AttributeComponent)
        if not target_attr:
            return False
        
        target_health = target_attr.get_attribute(AttributeType.CURRENT_HEALTH)
        if target_health <= 0:
            return False
        
        # 检查攻击距离
        distance = self.calculate_distance(attacker_id, target_id)
        attacker_attr = attacker_entity.get_component(AttributeComponent)
        if attacker_attr:
            attack_range = attacker_attr.get_attribute(AttributeType.DISTANCE)
            return distance <= attack_range
        
        return False
    
    def get_available_targets(self, user_id: str) -> List[str]:
        """获取可攻击的目标列表"""
        targets = []
        for target_id in self.room.players:
            if target_id != user_id and self.can_attack(user_id, target_id):
                targets.append(target_id)
        return targets
    
    def get_skill_info(self, user_id: str, skill_id: str) -> Optional[Dict[str, Any]]:
        """获取技能信息"""
        if user_id not in self.room.players:
            return None
        
        player_info = self.room.players[user_id]
        if not player_info.character_entity:
            return None
        
        return SkillFactory.get_skill_info(player_info.character_entity, skill_id)
    
    def get_all_skills(self, user_id: str) -> Dict[str, Any]:
        """获取玩家所有技能信息"""
        if user_id not in self.room.players:
            return {"active_skills": [], "passive_skills": []}
        
        player_info = self.room.players[user_id]
        if not player_info.character_entity:
            return {"active_skills": [], "passive_skills": []}
        
        return SkillFactory.get_all_skills_info(player_info.character_entity)
    
    def _validate_skill_use(self, user_id: str) -> bool:
        """验证技能使用条件"""
        # 检查是否是当前玩家的回合
        if user_id != self.room.get_current_player():
            return False
        
        # 检查玩家状态
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if not player_state_machine:
            return False
        
        if player_state_machine.current_state != PlayerState.TURN_SKILL:
            return False
        
        # 检查玩家是否存活
        if user_id not in self.room.get_alive_players():
            return False
        
        return True
    
    def _check_defeated_players(self, result: SkillExecutionResult):
        """检查被击败的玩家"""
        for target_entity in result.targets_affected:
            attr_component = target_entity.get_component(AttributeComponent)
            if attr_component:
                current_health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
                if current_health <= 0:
                    # 玩家被击败
                    player_id = getattr(target_entity, 'player_id', None)
                    if player_id:
                        self._handle_player_defeat(player_id)
    
    def _handle_player_defeat(self, user_id: str):
        """处理玩家被击败"""
        player_state_machine = state_manager.get_player_state_machine(user_id)
        if player_state_machine:
            player_state_machine.transition_to(PlayerState.DEFEATED)
        
        # 发布玩家被击败事件
        event = Event(
            type=EventType.PLAYER_DEFEATED,
            source=self.room,
            data={
                "room_id": self.room.room_id,
                "user_id": user_id
            }
        )
        event_system.emit(event)
        
        # 检查游戏是否结束
        alive_players = self.room.get_alive_players()
        if len(alive_players) <= 1:
            winner = alive_players[0] if alive_players else None
            self.room.end_game(winner)
        
        logger.info(f"Player {user_id} was defeated in room {self.room.room_id}")


class CombatCalculator:
    """战斗计算器"""
    
    @staticmethod
    def calculate_damage(attacker_entity, target_entity, base_damage: float, 
                        attack_ratio: float = 1.0, is_true_damage: bool = False) -> Dict[str, Any]:
        """计算伤害"""
        attacker_attr = attacker_entity.get_component(AttributeComponent)
        target_attr = target_entity.get_component(AttributeComponent)
        
        if not attacker_attr or not target_attr:
            return {"damage": 0, "is_crit": False}
        
        # 计算基础伤害
        attack_power = attacker_attr.get_attribute(AttributeType.ATTACK)
        damage = base_damage + (attack_power * attack_ratio)
        
        # 暴击计算
        crit_rate = attacker_attr.get_attribute(AttributeType.CRIT_RATE)
        is_crit = random.random() * 100 < crit_rate
        
        if is_crit:
            crit_damage = attacker_attr.get_attribute(AttributeType.CRIT_DAMAGE)
            damage *= crit_damage
        
        # 防御计算（非真实伤害）
        if not is_true_damage:
            defense = target_attr.get_attribute(AttributeType.DEFENSE)
            damage = CombatCalculator._apply_defense_reduction(damage, defense)
        
        return {
            "damage": max(0, int(damage)),
            "is_crit": is_crit,
            "is_true_damage": is_true_damage
        }
    
    @staticmethod
    def _apply_defense_reduction(damage: float, defense: float) -> float:
        """应用防御减伤"""
        # 使用原版的防御计算公式
        percent = 0.0
        if defense <= 100:
            percent = defense * 0.0015
        else:
            if defense <= 500:
                percent = 100 * 0.0015 + (defense - 100) * 0.0007
            elif 500 < defense <= 1000:
                percent = 100 * 0.0015 + 400 * 0.0007 + (defense - 500) * 0.0005
            else:
                percent = 100 * 0.0015 + 400 * 0.0007 + 500 * 0.0005
        
        return damage * (1 - percent)
