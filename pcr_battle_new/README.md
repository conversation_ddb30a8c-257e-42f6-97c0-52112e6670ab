# PCR大乱斗插件 - 新架构版本

一个基于NoneBot2的轻量级、可扩展的回合制战斗游戏插件。

## 🎯 设计目标

- **轻量级**: 核心代码简洁，运行高效
- **可扩展**: 模块化设计，易于添加新功能
- **可维护**: 清晰的架构分层，职责分离
- **稳定性**: 完善的错误处理和状态管理

## 🏗️ 架构设计

### 分层架构

```
┌─────────────────────────────────────────┐
│              NoneBot 接口层               │  命令处理、消息构建、图片渲染
├─────────────────────────────────────────┤
│              游戏逻辑层                   │  房间管理、回合系统、战斗系统
├─────────────────────────────────────────┤
│              角色系统层                   │  角色工厂、技能系统、属性管理
├─────────────────────────────────────────┤
│              核心引擎层                   │  事件系统、组件系统、状态管理
└─────────────────────────────────────────┘
```

### 核心模块

#### 1. 核心引擎 (`core/`)
- **事件系统**: 发布-订阅模式，实现模块间解耦
- **组件系统**: ECS架构，支持灵活的功能组合
- **状态管理**: 状态机模式，管理游戏和玩家状态

#### 2. 角色系统 (`character/`)
- **角色工厂**: 负责角色的创建和配置
- **属性组件**: 管理角色的各种属性
- **技能组件**: 管理角色的技能列表
- **Buff组件**: 管理角色的状态效果

#### 3. 技能系统 (`skill/`)
- **技能工厂**: 技能的创建和执行
- **效果策略**: 使用策略模式实现各种技能效果
- **触发器**: 定义技能的触发条件和目标选择

#### 4. 游戏逻辑 (`game/`)
- **房间管理**: 游戏房间的生命周期管理
- **回合系统**: 回合制战斗的流程控制
- **战斗系统**: 战斗相关的逻辑处理
- **跑道系统**: 地图事件和玩家移动

#### 5. 接口层 (`interface/`)
- **命令处理**: 处理NoneBot命令
- **消息构建**: 构建各种游戏消息
- **图片渲染**: 生成游戏画面

## 🎮 游戏功能

### 基础功能
- ✅ 房间创建和管理
- ✅ 玩家加入和离开
- ✅ 角色选择系统
- ✅ 回合制战斗
- ✅ 技能系统
- ✅ 跑道事件
- ✅ 游戏画面渲染

### 角色系统
- ✅ 多样化的角色选择
- ✅ 属性系统（生命值、攻击力、防御力等）
- ✅ 技能系统（主动技能、被动技能）
- ✅ Buff系统（增益、减益效果）

### 战斗系统
- ✅ 回合制战斗
- ✅ 距离计算
- ✅ 伤害计算（暴击、防御减伤）
- ✅ 技能效果（伤害、治疗、属性修改等）

## 📋 命令列表

| 命令 | 描述 |
|------|------|
| `创建大乱斗` | 创建游戏房间 |
| `加入大乱斗` | 加入游戏房间 |
| `开始大乱斗` | 开始游戏（房主） |
| `结束大乱斗` | 结束游戏（房主） |
| `角色列表` | 查看可选角色 |
| `查看状态` | 查看游戏状态 |
| `大乱斗规则` | 查看游戏规则 |
| `丢色子` | 投掷色子移动 |
| `技能 [编号] [目标]` | 使用技能 |
| `认输` | 主动认输 |

## 🚀 快速开始

### 安装依赖

```bash
pip install nonebot2[fastapi]
pip install nonebot-adapter-onebot
pip install pillow
```

### 配置插件

在NoneBot项目中添加插件：

```python
# bot.py
import nonebot
from nonebot.adapters.onebot.v11 import Adapter as ONEBOT_V11Adapter

nonebot.init()
driver = nonebot.get_driver()
driver.register_adapter(ONEBOT_V11Adapter)

# 加载插件
nonebot.load_plugin("pcr_battle_new")

if __name__ == "__main__":
    nonebot.run()
```

### 开始游戏

1. 在群聊中发送 `创建大乱斗`
2. 其他玩家发送 `加入大乱斗`
3. 房主发送 `开始大乱斗`
4. 选择角色（直接发送角色名称）
5. 开始游戏！

## 🔧 扩展开发

### 添加新角色

```python
from pcr_battle_new.character.character_data import CharacterData, Position, AttributeType, SkillData, register_character

# 定义角色数据
new_character = CharacterData(
    id="new_character",
    name="新角色",
    position=Position.DPS,
    base_attributes={
        AttributeType.MAX_HEALTH: 1000,
        AttributeType.ATTACK: 150,
        AttributeType.DEFENSE: 80,
        # ... 其他属性
    },
    active_skills=[
        SkillData(
            id="new_skill",
            name="新技能",
            description="技能描述",
            tp_cost=30,
            trigger_type="select_except_me",
            effects={"damage": {"base": 100, "attack_ratio": 1.2}}
        )
    ]
)

# 注册角色
register_character(new_character)
```

### 添加新技能效果

```python
from pcr_battle_new.skill.effect_strategies import EffectStrategy, register_effect_strategy

class CustomEffect(EffectStrategy):
    def apply(self, caster, target, effect_data, context):
        # 实现自定义效果
        return {"success": True, "message": "自定义效果生效"}

# 注册效果策略
register_effect_strategy("custom_effect", CustomEffect())
```

### 添加新跑道事件

```python
from pcr_battle_new.game.runway_system import RunwayEvent, RunwayEventType

def custom_event_handler(character_entity, event):
    # 实现自定义事件处理
    return ["自定义事件触发了！"]

custom_event = RunwayEvent(
    event_type=RunwayEventType.BUFF,
    name="自定义事件",
    description="这是一个自定义事件",
    effect_range=(1, 1),
    effect_handler=custom_event_handler
)
```

## 🧪 测试

运行测试脚本验证架构：

```python
python -m pcr_battle_new.test_new_architecture
```

## 📈 性能优化

- 事件系统使用异步处理，避免阻塞
- 组件系统支持按需更新
- 状态机减少不必要的状态检查
- 图片渲染使用缓存机制

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🙏 致谢

感谢原版PCR大乱斗插件的创意和灵感！
