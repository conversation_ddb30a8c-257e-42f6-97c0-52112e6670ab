"""
角色工厂

负责创建和配置角色实体
"""

from typing import Optional, Dict, Any
import logging

from ..core.component_system import Entity, component_system
from ..core.event_system import Event, EventType, event_system
from .character_data import CharacterData, get_character_data, AttributeType
from .components import AttributeComponent, SkillComponent, BuffComponent

logger = logging.getLogger(__name__)


class CharacterFactory:
    """角色工厂"""
    
    @staticmethod
    def create_character(character_id: str, player_id: str, entity_id: Optional[str] = None) -> Optional[Entity]:
        """创建角色实体"""
        # 获取角色数据
        character_data = get_character_data(character_id)
        if not character_data:
            logger.error(f"Character data not found for id: {character_id}")
            return None
        
        # 创建实体
        entity = component_system.create_entity(entity_id)
        
        # 添加属性组件
        attr_component = AttributeComponent(entity.id)
        CharacterFactory._setup_attributes(attr_component, character_data)
        entity.add_component(attr_component)
        
        # 添加技能组件
        skill_component = SkillComponent(entity.id)
        CharacterFactory._setup_skills(skill_component, character_data)
        entity.add_component(skill_component)
        
        # 添加Buff组件
        buff_component = BuffComponent(entity.id)
        entity.add_component(buff_component)
        
        # 存储角色相关信息
        entity.character_id = character_id
        entity.character_data = character_data
        entity.player_id = player_id
        
        # 发布角色创建事件
        event = Event(
            type=EventType.PLAYER_SELECTED_CHARACTER,
            source=entity,
            data={
                "player_id": player_id,
                "character_id": character_id,
                "entity_id": entity.id
            }
        )
        event_system.emit(event)
        
        logger.info(f"Created character {character_data.name} for player {player_id}")
        return entity
    
    @staticmethod
    def _setup_attributes(attr_component: AttributeComponent, character_data: CharacterData):
        """设置角色属性"""
        # 设置基础属性
        for attr_type, value in character_data.base_attributes.items():
            attr_component.set_base_attribute(attr_type, value)
        
        # 确保当前生命值等于最大生命值
        if AttributeType.MAX_HEALTH in character_data.base_attributes:
            max_health = character_data.base_attributes[AttributeType.MAX_HEALTH]
            attr_component.set_base_attribute(AttributeType.CURRENT_HEALTH, max_health)
        
        # 设置默认的TP值
        if AttributeType.MAX_TP not in character_data.base_attributes:
            attr_component.set_base_attribute(AttributeType.MAX_TP, 100)
        
        if AttributeType.CURRENT_TP not in character_data.base_attributes:
            attr_component.set_base_attribute(AttributeType.CURRENT_TP, 20)
        
        # 设置默认暴击伤害
        if AttributeType.CRIT_DAMAGE not in character_data.base_attributes:
            attr_component.set_base_attribute(AttributeType.CRIT_DAMAGE, 2.0)
    
    @staticmethod
    def _setup_skills(skill_component: SkillComponent, character_data: CharacterData):
        """设置角色技能"""
        # 添加主动技能
        for skill_data in character_data.active_skills:
            skill_component.add_skill(skill_data, is_passive=False)
        
        # 添加被动技能
        for skill_data in character_data.passive_skills:
            skill_component.add_skill(skill_data, is_passive=True)
    
    @staticmethod
    def get_character_info(entity: Entity) -> Dict[str, Any]:
        """获取角色信息"""
        if not hasattr(entity, 'character_data'):
            return {}
        
        attr_component = entity.get_component(AttributeComponent)
        skill_component = entity.get_component(SkillComponent)
        buff_component = entity.get_component(BuffComponent)
        
        info = {
            "id": entity.character_id,
            "name": entity.character_data.name,
            "position": entity.character_data.position.value,
            "player_id": entity.player_id,
        }
        
        if attr_component:
            info["attributes"] = {
                attr_type.value: attr_component.get_attribute(attr_type)
                for attr_type in AttributeType
                if attr_component.get_attribute(attr_type) != 0
            }
        
        if skill_component:
            info["active_skills"] = [
                {
                    "id": skill.id,
                    "name": skill.name,
                    "description": skill.description,
                    "tp_cost": skill.tp_cost
                }
                for skill in skill_component.active_skills
            ]
        
        if buff_component:
            info["buffs"] = [
                {
                    "id": buff.id,
                    "name": buff.name,
                    "description": buff.description,
                    "remaining_turns": buff.remaining_turns,
                    "stack_count": buff.stack_count
                }
                for buff in buff_component.buffs.values()
            ]
        
        return info
    
    @staticmethod
    def destroy_character(entity: Entity):
        """销毁角色"""
        if hasattr(entity, 'player_id'):
            # 发布角色销毁事件
            event = Event(
                type=EventType.PLAYER_LEFT,
                source=entity,
                data={
                    "player_id": entity.player_id,
                    "entity_id": entity.id
                }
            )
            event_system.emit(event)
        
        # 从组件系统中移除
        component_system.destroy_entity(entity.id)
        
        logger.info(f"Destroyed character entity {entity.id}")


# 便利函数
def create_character(character_id: str, player_id: str) -> Optional[Entity]:
    """创建角色的便利函数"""
    return CharacterFactory.create_character(character_id, player_id)


def get_character_info(entity: Entity) -> Dict[str, Any]:
    """获取角色信息的便利函数"""
    return CharacterFactory.get_character_info(entity)
