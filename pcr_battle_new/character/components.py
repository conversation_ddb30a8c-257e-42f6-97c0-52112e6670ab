"""
角色组件

定义角色相关的各种组件
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import time

from ..core.component_system import Component
from .character_data import AttributeType, SkillData


@dataclass
class BuffData:
    """Buff数据"""
    id: str
    name: str
    description: str
    effect_type: str
    value: float
    duration: int  # 持续回合数，-1表示永久
    remaining_turns: int
    stack_count: int = 1
    max_stacks: int = 1
    
    def __post_init__(self):
        if self.remaining_turns == 0:
            self.remaining_turns = self.duration


class AttributeComponent(Component):
    """属性组件"""
    
    def __init__(self, entity_id: str):
        super().__init__(entity_id)
        self.base_attributes: Dict[AttributeType, float] = {}
        self.current_attributes: Dict[AttributeType, float] = {}
        self.attribute_modifiers: Dict[AttributeType, List[float]] = {}
    
    def set_base_attribute(self, attr_type: AttributeType, value: float):
        """设置基础属性"""
        self.base_attributes[attr_type] = value
        self._recalculate_attribute(attr_type)
    
    def get_attribute(self, attr_type: AttributeType) -> float:
        """获取属性值"""
        return self.current_attributes.get(attr_type, 0.0)
    
    def add_modifier(self, attr_type: AttributeType, value: float):
        """添加属性修正值"""
        if attr_type not in self.attribute_modifiers:
            self.attribute_modifiers[attr_type] = []
        self.attribute_modifiers[attr_type].append(value)
        self._recalculate_attribute(attr_type)
    
    def remove_modifier(self, attr_type: AttributeType, value: float):
        """移除属性修正值"""
        if attr_type in self.attribute_modifiers:
            try:
                self.attribute_modifiers[attr_type].remove(value)
                self._recalculate_attribute(attr_type)
            except ValueError:
                pass
    
    def modify_attribute(self, attr_type: AttributeType, value: float, respect_limits: bool = True):
        """直接修改属性值"""
        current = self.get_attribute(attr_type)
        new_value = current + value
        
        if respect_limits:
            new_value = self._apply_limits(attr_type, new_value)
        
        self.current_attributes[attr_type] = new_value
        
        # 处理特殊属性的联动
        self._handle_attribute_dependencies(attr_type, value)
    
    def _recalculate_attribute(self, attr_type: AttributeType):
        """重新计算属性值"""
        base_value = self.base_attributes.get(attr_type, 0.0)
        modifiers = self.attribute_modifiers.get(attr_type, [])
        
        total_value = base_value + sum(modifiers)
        total_value = self._apply_limits(attr_type, total_value)
        
        self.current_attributes[attr_type] = total_value
    
    def _apply_limits(self, attr_type: AttributeType, value: float) -> float:
        """应用属性限制"""
        # 生命值不能超过最大值
        if attr_type == AttributeType.CURRENT_HEALTH:
            max_health = self.get_attribute(AttributeType.MAX_HEALTH)
            value = min(value, max_health)
        
        # TP不能超过最大值
        elif attr_type == AttributeType.CURRENT_TP:
            max_tp = self.get_attribute(AttributeType.MAX_TP)
            value = min(value, max_tp)
        
        # 暴击率限制在0-100%
        elif attr_type == AttributeType.CRIT_RATE:
            value = max(0, min(100, value))
        
        # 攻击距离限制
        elif attr_type == AttributeType.DISTANCE:
            value = max(1, min(15, value))
        
        # 大部分属性不能为负数
        if attr_type in [AttributeType.MAX_HEALTH, AttributeType.CURRENT_HEALTH,
                        AttributeType.ATTACK, AttributeType.DEFENSE,
                        AttributeType.MAX_TP, AttributeType.CURRENT_TP]:
            value = max(0, value)
        
        return value
    
    def _handle_attribute_dependencies(self, attr_type: AttributeType, change_value: float):
        """处理属性依赖关系"""
        # 最大生命值增加时，当前生命值也增加
        if attr_type == AttributeType.MAX_HEALTH and change_value > 0:
            self.modify_attribute(AttributeType.CURRENT_HEALTH, change_value, False)
        
        # 最大TP增加时，当前TP也增加
        elif attr_type == AttributeType.MAX_TP and change_value > 0:
            self.modify_attribute(AttributeType.CURRENT_TP, change_value, False)
        
        # 更新已消耗生命值
        elif attr_type in [AttributeType.CURRENT_HEALTH, AttributeType.MAX_HEALTH]:
            max_health = self.get_attribute(AttributeType.MAX_HEALTH)
            current_health = self.get_attribute(AttributeType.CURRENT_HEALTH)
            cost_health = max_health - current_health
            self.current_attributes[AttributeType.COST_HEALTH] = cost_health
    
    def update(self, delta_time: float):
        """更新组件"""
        pass


class SkillComponent(Component):
    """技能组件"""
    
    def __init__(self, entity_id: str):
        super().__init__(entity_id)
        self.active_skills: List[SkillData] = []
        self.passive_skills: List[SkillData] = []
        self.skill_cooldowns: Dict[str, float] = {}
    
    def add_skill(self, skill: SkillData, is_passive: bool = False):
        """添加技能"""
        if is_passive:
            self.passive_skills.append(skill)
        else:
            self.active_skills.append(skill)
    
    def remove_skill(self, skill_id: str):
        """移除技能"""
        self.active_skills = [s for s in self.active_skills if s.id != skill_id]
        self.passive_skills = [s for s in self.passive_skills if s.id != skill_id]
        self.skill_cooldowns.pop(skill_id, None)
    
    def get_skill(self, skill_id: str) -> Optional[SkillData]:
        """获取技能"""
        for skill in self.active_skills + self.passive_skills:
            if skill.id == skill_id:
                return skill
        return None
    
    def can_use_skill(self, skill_id: str, current_tp: float) -> bool:
        """检查是否可以使用技能"""
        skill = self.get_skill(skill_id)
        if not skill:
            return False
        
        # 检查TP是否足够
        if skill.tp_cost > current_tp:
            return False
        
        # 检查冷却时间
        if skill_id in self.skill_cooldowns:
            if time.time() < self.skill_cooldowns[skill_id]:
                return False
        
        return True
    
    def set_cooldown(self, skill_id: str, cooldown_seconds: float):
        """设置技能冷却"""
        self.skill_cooldowns[skill_id] = time.time() + cooldown_seconds
    
    def update(self, delta_time: float):
        """更新组件"""
        # 清理过期的冷却时间
        current_time = time.time()
        expired_cooldowns = [skill_id for skill_id, end_time in self.skill_cooldowns.items() 
                           if current_time >= end_time]
        for skill_id in expired_cooldowns:
            del self.skill_cooldowns[skill_id]


class BuffComponent(Component):
    """Buff组件"""
    
    def __init__(self, entity_id: str):
        super().__init__(entity_id)
        self.buffs: Dict[str, BuffData] = {}
    
    def add_buff(self, buff: BuffData):
        """添加Buff"""
        if buff.id in self.buffs:
            existing_buff = self.buffs[buff.id]
            # 如果可以叠加
            if existing_buff.stack_count < existing_buff.max_stacks:
                existing_buff.stack_count += 1
            # 刷新持续时间
            existing_buff.remaining_turns = buff.duration
        else:
            self.buffs[buff.id] = buff
    
    def remove_buff(self, buff_id: str) -> Optional[BuffData]:
        """移除Buff"""
        return self.buffs.pop(buff_id, None)
    
    def get_buff(self, buff_id: str) -> Optional[BuffData]:
        """获取Buff"""
        return self.buffs.get(buff_id)
    
    def has_buff(self, buff_id: str) -> bool:
        """检查是否有指定Buff"""
        return buff_id in self.buffs
    
    def get_buffs_by_type(self, effect_type: str) -> List[BuffData]:
        """根据效果类型获取Buff"""
        return [buff for buff in self.buffs.values() if buff.effect_type == effect_type]
    
    def process_turn_end(self):
        """处理回合结束时的Buff更新"""
        expired_buffs = []
        
        for buff_id, buff in self.buffs.items():
            if buff.remaining_turns > 0:
                buff.remaining_turns -= 1
                if buff.remaining_turns <= 0:
                    expired_buffs.append(buff_id)
        
        # 移除过期的Buff
        for buff_id in expired_buffs:
            self.remove_buff(buff_id)
        
        return expired_buffs
    
    def update(self, delta_time: float):
        """更新组件"""
        pass
