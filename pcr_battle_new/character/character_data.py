"""
角色数据定义

定义角色的基础数据结构和属性类型
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class AttributeType(Enum):
    """属性类型枚举"""
    # 基础属性
    MAX_HEALTH = "max_health"        # 最大生命值
    CURRENT_HEALTH = "current_health" # 当前生命值
    ATTACK = "attack"                # 攻击力
    DEFENSE = "defense"              # 防御力
    DISTANCE = "distance"            # 攻击距离
    CRIT_RATE = "crit_rate"         # 暴击率
    CRIT_DAMAGE = "crit_damage"     # 暴击伤害
    
    # 特殊属性
    MAX_TP = "max_tp"               # 最大TP值
    CURRENT_TP = "current_tp"       # 当前TP值
    ATTACK_SPEED = "attack_speed"   # 攻击速度
    
    # 计算属性
    COST_HEALTH = "cost_health"     # 已消耗生命值


class Position(Enum):
    """角色定位"""
    TANK = "tank"           # 坦克
    DPS = "dps"            # 输出
    SUPPORT = "support"    # 辅助
    HEALER = "healer"      # 治疗


@dataclass
class SkillData:
    """技能数据"""
    id: str
    name: str
    description: str
    tp_cost: int
    trigger_type: str
    effects: Dict[str, Any]
    passive_skills: List[str] = None
    
    def __post_init__(self):
        if self.passive_skills is None:
            self.passive_skills = []


@dataclass
class CharacterData:
    """角色数据"""
    id: str
    name: str
    position: Position
    icon_path: Optional[str] = None
    
    # 基础属性
    base_attributes: Dict[AttributeType, float] = None
    
    # 技能列表
    active_skills: List[SkillData] = None
    passive_skills: List[SkillData] = None
    
    # 被动能力
    passive_ability: Optional[str] = None
    
    def __post_init__(self):
        if self.base_attributes is None:
            self.base_attributes = {}
        if self.active_skills is None:
            self.active_skills = []
        if self.passive_skills is None:
            self.passive_skills = []


# 角色数据注册表
CHARACTER_REGISTRY: Dict[str, CharacterData] = {}


def register_character(character_data: CharacterData):
    """注册角色数据"""
    CHARACTER_REGISTRY[character_data.id] = character_data


def get_character_data(character_id: str) -> Optional[CharacterData]:
    """获取角色数据"""
    return CHARACTER_REGISTRY.get(character_id)


def get_all_characters() -> Dict[str, CharacterData]:
    """获取所有角色数据"""
    return CHARACTER_REGISTRY.copy()


# 预定义一些基础角色数据
def _register_default_characters():
    """注册默认角色"""
    
    # 日和 - 近战DPS
    hiyori = CharacterData(
        id="hiyori",
        name="日和",
        position=Position.DPS,
        base_attributes={
            AttributeType.MAX_HEALTH: 800,
            AttributeType.ATTACK: 120,
            AttributeType.DEFENSE: 60,
            AttributeType.DISTANCE: 3,
            AttributeType.CRIT_RATE: 15,
            AttributeType.CRIT_DAMAGE: 2.0,
            AttributeType.CURRENT_TP: 20,
            AttributeType.MAX_TP: 100,
        },
        active_skills=[
            SkillData(
                id="hiyori_normal_attack",
                name="普通攻击",
                description="对目标造成1.0倍攻击力的伤害",
                tp_cost=0,
                trigger_type="select_except_me",
                effects={"damage": {"base": 0, "attack_ratio": 1.0}}
            ),
            SkillData(
                id="hiyori_cat_punch",
                name="猫拳连击",
                description="对目标造成1.5倍攻击力的伤害，有30%几率再次攻击",
                tp_cost=25,
                trigger_type="select_except_me",
                effects={
                    "damage": {"base": 0, "attack_ratio": 1.5},
                    "extra_attack": {"chance": 0.3, "damage_ratio": 0.8}
                }
            )
        ]
    )
    register_character(hiyori)
    
    # 优衣 - 辅助
    yui = CharacterData(
        id="yui",
        name="优衣",
        position=Position.SUPPORT,
        base_attributes={
            AttributeType.MAX_HEALTH: 600,
            AttributeType.ATTACK: 80,
            AttributeType.DEFENSE: 40,
            AttributeType.DISTANCE: 5,
            AttributeType.CRIT_RATE: 10,
            AttributeType.CRIT_DAMAGE: 1.8,
            AttributeType.CURRENT_TP: 30,
            AttributeType.MAX_TP: 100,
        },
        active_skills=[
            SkillData(
                id="yui_normal_attack",
                name="普通攻击",
                description="对目标造成1.0倍攻击力的伤害",
                tp_cost=0,
                trigger_type="select_except_me",
                effects={"damage": {"base": 0, "attack_ratio": 1.0}}
            ),
            SkillData(
                id="yui_heal",
                name="治疗术",
                description="恢复目标200点生命值",
                tp_cost=20,
                trigger_type="select",
                effects={"heal": {"base": 200, "attack_ratio": 0.5}}
            )
        ]
    )
    register_character(yui)


# 初始化默认角色
_register_default_characters()
