"""
组件系统

提供基于组件的实体架构，支持灵活的功能组合
"""

from typing import Dict, Type, TypeVar, Optional, Any
from abc import ABC, abstractmethod
import uuid

T = TypeVar('T', bound='Component')


class Component(ABC):
    """组件基类"""
    
    def __init__(self, entity_id: str):
        self.entity_id = entity_id
        self.enabled = True
    
    @abstractmethod
    def update(self, delta_time: float):
        """更新组件状态"""
        pass
    
    def enable(self):
        """启用组件"""
        self.enabled = True
    
    def disable(self):
        """禁用组件"""
        self.enabled = False


class Entity:
    """实体类"""
    
    def __init__(self, entity_id: Optional[str] = None):
        self.id = entity_id or str(uuid.uuid4())
        self._components: Dict[Type[Component], Component] = {}
    
    def add_component(self, component: Component) -> Component:
        """添加组件"""
        component_type = type(component)
        self._components[component_type] = component
        return component
    
    def remove_component(self, component_type: Type[T]) -> Optional[T]:
        """移除组件"""
        return self._components.pop(component_type, None)
    
    def get_component(self, component_type: Type[T]) -> Optional[T]:
        """获取组件"""
        return self._components.get(component_type)
    
    def has_component(self, component_type: Type[Component]) -> bool:
        """检查是否有指定组件"""
        return component_type in self._components
    
    def get_all_components(self) -> Dict[Type[Component], Component]:
        """获取所有组件"""
        return self._components.copy()
    
    def update(self, delta_time: float):
        """更新所有组件"""
        for component in self._components.values():
            if component.enabled:
                component.update(delta_time)


class ComponentSystem:
    """组件系统"""
    
    def __init__(self):
        self._entities: Dict[str, Entity] = {}
    
    def create_entity(self, entity_id: Optional[str] = None) -> Entity:
        """创建实体"""
        entity = Entity(entity_id)
        self._entities[entity.id] = entity
        return entity
    
    def destroy_entity(self, entity_id: str) -> bool:
        """销毁实体"""
        return self._entities.pop(entity_id, None) is not None
    
    def get_entity(self, entity_id: str) -> Optional[Entity]:
        """获取实体"""
        return self._entities.get(entity_id)
    
    def get_entities_with_component(self, component_type: Type[T]) -> Dict[str, Entity]:
        """获取拥有指定组件的所有实体"""
        result = {}
        for entity_id, entity in self._entities.items():
            if entity.has_component(component_type):
                result[entity_id] = entity
        return result
    
    def update_all(self, delta_time: float):
        """更新所有实体"""
        for entity in self._entities.values():
            entity.update(delta_time)
    
    def clear(self):
        """清除所有实体"""
        self._entities.clear()


# 全局组件系统实例
component_system = ComponentSystem()
