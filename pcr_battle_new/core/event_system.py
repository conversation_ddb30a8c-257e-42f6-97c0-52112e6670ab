"""
事件系统

提供事件的发布、订阅、处理机制，实现模块间的解耦
"""

from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum
import asyncio
import logging

logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    # 游戏生命周期事件
    GAME_CREATED = "game_created"
    GAME_STARTED = "game_started"
    GAME_ENDED = "game_ended"
    
    # 玩家事件
    PLAYER_JOINED = "player_joined"
    PLAYER_LEFT = "player_left"
    PLAYER_SELECTED_CHARACTER = "player_selected_character"
    
    # 回合事件
    TURN_STARTED = "turn_started"
    TURN_ENDED = "turn_ended"
    
    # 战斗事件
    DICE_THROWN = "dice_thrown"
    SKILL_USED = "skill_used"
    DAMAGE_DEALT = "damage_dealt"
    PLAYER_DEFEATED = "player_defeated"
    
    # 地图事件
    RUNWAY_EVENT_TRIGGERED = "runway_event_triggered"
    PLAYER_MOVED = "player_moved"
    
    # Buff事件
    BUFF_APPLIED = "buff_applied"
    BUFF_REMOVED = "buff_removed"
    BUFF_TRIGGERED = "buff_triggered"


@dataclass
class Event:
    """事件数据类"""
    type: EventType
    source: Any  # 事件源
    data: Dict[str, Any]  # 事件数据
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            import time
            self.timestamp = time.time()


class EventSystem:
    """事件系统"""
    
    def __init__(self):
        self._listeners: Dict[EventType, List[Callable]] = {}
        self._async_listeners: Dict[EventType, List[Callable]] = {}
        
    def subscribe(self, event_type: EventType, callback: Callable):
        """订阅事件（同步回调）"""
        if event_type not in self._listeners:
            self._listeners[event_type] = []
        self._listeners[event_type].append(callback)
        
    def subscribe_async(self, event_type: EventType, callback: Callable):
        """订阅事件（异步回调）"""
        if event_type not in self._async_listeners:
            self._async_listeners[event_type] = []
        self._async_listeners[event_type].append(callback)
        
    def unsubscribe(self, event_type: EventType, callback: Callable):
        """取消订阅事件"""
        if event_type in self._listeners:
            try:
                self._listeners[event_type].remove(callback)
            except ValueError:
                pass
                
        if event_type in self._async_listeners:
            try:
                self._async_listeners[event_type].remove(callback)
            except ValueError:
                pass
    
    def emit(self, event: Event):
        """发布事件（同步）"""
        try:
            # 处理同步监听器
            if event.type in self._listeners:
                for callback in self._listeners[event.type]:
                    try:
                        callback(event)
                    except Exception as e:
                        logger.error(f"Error in event listener: {e}")
        except Exception as e:
            logger.error(f"Error emitting event {event.type}: {e}")
    
    async def emit_async(self, event: Event):
        """发布事件（异步）"""
        try:
            # 先处理同步监听器
            self.emit(event)
            
            # 处理异步监听器
            if event.type in self._async_listeners:
                tasks = []
                for callback in self._async_listeners[event.type]:
                    try:
                        task = asyncio.create_task(callback(event))
                        tasks.append(task)
                    except Exception as e:
                        logger.error(f"Error creating async task for event listener: {e}")
                
                if tasks:
                    await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error emitting async event {event.type}: {e}")
    
    def clear_listeners(self, event_type: Optional[EventType] = None):
        """清除监听器"""
        if event_type:
            self._listeners.pop(event_type, None)
            self._async_listeners.pop(event_type, None)
        else:
            self._listeners.clear()
            self._async_listeners.clear()


# 全局事件系统实例
event_system = EventSystem()
