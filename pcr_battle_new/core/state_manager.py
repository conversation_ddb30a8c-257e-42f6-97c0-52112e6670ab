"""
状态管理器

提供游戏状态的统一管理和状态机功能
"""

from typing import Dict, Any, Optional, Callable, Set
from enum import Enum
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)


class GameState(Enum):
    """游戏状态枚举"""
    IDLE = "idle"                    # 空闲状态
    WAITING = "waiting"              # 等待玩家加入
    CHARACTER_SELECT = "char_select" # 角色选择阶段
    PLAYING = "playing"              # 游戏进行中
    PAUSED = "paused"               # 游戏暂停
    ENDED = "ended"                 # 游戏结束


class PlayerState(Enum):
    """玩家状态枚举"""
    WAITING = "waiting"              # 等待中
    SELECTING_CHARACTER = "selecting_char"  # 选择角色中
    READY = "ready"                  # 准备就绪
    TURN_DICE = "turn_dice"         # 轮到丢色子
    TURN_SKILL = "turn_skill"       # 轮到使用技能
    DEFEATED = "defeated"           # 被击败
    TEMPORARILY_OUT = "temp_out"    # 暂时出局（可复活）


@dataclass
class StateTransition:
    """状态转换定义"""
    from_state: Any
    to_state: Any
    condition: Optional[Callable] = None
    action: Optional[Callable] = None


class StateMachine:
    """状态机"""
    
    def __init__(self, initial_state: Any):
        self.current_state = initial_state
        self.transitions: Dict[Any, Dict[Any, StateTransition]] = {}
        self.state_enter_callbacks: Dict[Any, Set[Callable]] = {}
        self.state_exit_callbacks: Dict[Any, Set[Callable]] = {}
    
    def add_transition(self, transition: StateTransition):
        """添加状态转换"""
        if transition.from_state not in self.transitions:
            self.transitions[transition.from_state] = {}
        self.transitions[transition.from_state][transition.to_state] = transition
    
    def add_state_enter_callback(self, state: Any, callback: Callable):
        """添加状态进入回调"""
        if state not in self.state_enter_callbacks:
            self.state_enter_callbacks[state] = set()
        self.state_enter_callbacks[state].add(callback)
    
    def add_state_exit_callback(self, state: Any, callback: Callable):
        """添加状态退出回调"""
        if state not in self.state_exit_callbacks:
            self.state_exit_callbacks[state] = set()
        self.state_exit_callbacks[state].add(callback)
    
    def can_transition_to(self, target_state: Any) -> bool:
        """检查是否可以转换到目标状态"""
        if self.current_state not in self.transitions:
            return False
        if target_state not in self.transitions[self.current_state]:
            return False
        
        transition = self.transitions[self.current_state][target_state]
        if transition.condition:
            return transition.condition()
        return True
    
    def transition_to(self, target_state: Any, **kwargs) -> bool:
        """转换到目标状态"""
        if not self.can_transition_to(target_state):
            logger.warning(f"Cannot transition from {self.current_state} to {target_state}")
            return False
        
        old_state = self.current_state
        transition = self.transitions[self.current_state][target_state]
        
        # 执行退出回调
        if old_state in self.state_exit_callbacks:
            for callback in self.state_exit_callbacks[old_state]:
                try:
                    callback(old_state, target_state, **kwargs)
                except Exception as e:
                    logger.error(f"Error in state exit callback: {e}")
        
        # 执行转换动作
        if transition.action:
            try:
                transition.action(old_state, target_state, **kwargs)
            except Exception as e:
                logger.error(f"Error in transition action: {e}")
        
        # 更新状态
        self.current_state = target_state
        
        # 执行进入回调
        if target_state in self.state_enter_callbacks:
            for callback in self.state_enter_callbacks[target_state]:
                try:
                    callback(old_state, target_state, **kwargs)
                except Exception as e:
                    logger.error(f"Error in state enter callback: {e}")
        
        logger.info(f"State transitioned from {old_state} to {target_state}")
        return True


class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self._game_states: Dict[str, StateMachine] = {}  # 游戏状态机
        self._player_states: Dict[str, StateMachine] = {}  # 玩家状态机
        self._global_data: Dict[str, Any] = {}  # 全局数据
    
    def create_game_state_machine(self, game_id: str) -> StateMachine:
        """创建游戏状态机"""
        state_machine = StateMachine(GameState.IDLE)
        self._setup_game_transitions(state_machine)
        self._game_states[game_id] = state_machine
        return state_machine
    
    def create_player_state_machine(self, player_id: str) -> StateMachine:
        """创建玩家状态机"""
        state_machine = StateMachine(PlayerState.WAITING)
        self._setup_player_transitions(state_machine)
        self._player_states[player_id] = state_machine
        return state_machine
    
    def get_game_state_machine(self, game_id: str) -> Optional[StateMachine]:
        """获取游戏状态机"""
        return self._game_states.get(game_id)
    
    def get_player_state_machine(self, player_id: str) -> Optional[StateMachine]:
        """获取玩家状态机"""
        return self._player_states.get(player_id)
    
    def remove_game_state_machine(self, game_id: str):
        """移除游戏状态机"""
        self._game_states.pop(game_id, None)
    
    def remove_player_state_machine(self, player_id: str):
        """移除玩家状态机"""
        self._player_states.pop(player_id, None)
    
    def set_global_data(self, key: str, value: Any):
        """设置全局数据"""
        self._global_data[key] = value
    
    def get_global_data(self, key: str, default: Any = None) -> Any:
        """获取全局数据"""
        return self._global_data.get(key, default)
    
    def _setup_game_transitions(self, state_machine: StateMachine):
        """设置游戏状态转换"""
        # 定义游戏状态转换规则
        transitions = [
            StateTransition(GameState.IDLE, GameState.WAITING),
            StateTransition(GameState.WAITING, GameState.CHARACTER_SELECT),
            StateTransition(GameState.CHARACTER_SELECT, GameState.PLAYING),
            StateTransition(GameState.PLAYING, GameState.PAUSED),
            StateTransition(GameState.PAUSED, GameState.PLAYING),
            StateTransition(GameState.PLAYING, GameState.ENDED),
            StateTransition(GameState.ENDED, GameState.IDLE),
        ]
        
        for transition in transitions:
            state_machine.add_transition(transition)
    
    def _setup_player_transitions(self, state_machine: StateMachine):
        """设置玩家状态转换"""
        # 定义玩家状态转换规则
        transitions = [
            StateTransition(PlayerState.WAITING, PlayerState.SELECTING_CHARACTER),
            StateTransition(PlayerState.SELECTING_CHARACTER, PlayerState.READY),
            StateTransition(PlayerState.READY, PlayerState.TURN_DICE),
            StateTransition(PlayerState.TURN_DICE, PlayerState.TURN_SKILL),
            StateTransition(PlayerState.TURN_SKILL, PlayerState.WAITING),
            StateTransition(PlayerState.TURN_DICE, PlayerState.DEFEATED),
            StateTransition(PlayerState.TURN_SKILL, PlayerState.DEFEATED),
            StateTransition(PlayerState.DEFEATED, PlayerState.TEMPORARILY_OUT),
            StateTransition(PlayerState.TEMPORARILY_OUT, PlayerState.READY),
        ]
        
        for transition in transitions:
            state_machine.add_transition(transition)


# 全局状态管理器实例
state_manager = StateManager()
