"""
PCR大乱斗插件 - 新架构版本

一个轻量级、可扩展的回合制战斗游戏插件
"""

from nonebot import on_command, on_fullmatch, on_message
from nonebot.adapters.onebot.v11 import GroupMessageEvent

from .interface.command_handler import CommandHandler

# 创建命令处理器实例
command_handler = CommandHandler()

# 注册命令
create_game = on_fullmatch("创建大乱斗", priority=5, block=True)
join_game = on_fullmatch("加入大乱斗", priority=5, block=True)
start_game = on_fullmatch("开始大乱斗", priority=5, block=True)
end_game = on_fullmatch("结束大乱斗", priority=5, block=True)
game_status = on_fullmatch("查看状态", priority=5, block=True)
game_rules = on_fullmatch("大乱斗规则", priority=5, block=True)
character_list = on_fullmatch("角色列表", priority=5, block=True)
surrender = on_fullmatch("认输", priority=5, block=True)

# 游戏操作命令
throw_dice = on_command("丢色子", aliases={"丢"}, priority=5, block=True)
use_skill = on_command("技能", priority=5, block=True)
select_character = on_message(priority=999, block=True)

# 绑定处理函数
@create_game.handle()
async def handle_create_game(bot, event: GroupMessageEvent):
    await command_handler.handle_create_game(bot, event)

@join_game.handle()
async def handle_join_game(bot, event: GroupMessageEvent):
    await command_handler.handle_join_game(bot, event)

@start_game.handle()
async def handle_start_game(bot, event: GroupMessageEvent):
    await command_handler.handle_start_game(bot, event)

@end_game.handle()
async def handle_end_game(bot, event: GroupMessageEvent):
    await command_handler.handle_end_game(bot, event)

@game_status.handle()
async def handle_game_status(bot, event: GroupMessageEvent):
    await command_handler.handle_game_status(bot, event)

@game_rules.handle()
async def handle_game_rules(bot, event: GroupMessageEvent):
    await command_handler.handle_game_rules(bot, event)

@character_list.handle()
async def handle_character_list(bot, event: GroupMessageEvent):
    await command_handler.handle_character_list(bot, event)

@surrender.handle()
async def handle_surrender(bot, event: GroupMessageEvent):
    await command_handler.handle_surrender(bot, event)

@throw_dice.handle()
async def handle_throw_dice(bot, event: GroupMessageEvent):
    await command_handler.handle_throw_dice(bot, event)

@use_skill.handle()
async def handle_use_skill(bot, event: GroupMessageEvent):
    await command_handler.handle_use_skill(bot, event)

@select_character.handle()
async def handle_select_character(bot, event: GroupMessageEvent):
    await command_handler.handle_select_character(bot, event)

__version__ = "2.0.0"
__author__ = "Augment Agent"
__description__ = "PCR大乱斗插件 - 新架构版本"
