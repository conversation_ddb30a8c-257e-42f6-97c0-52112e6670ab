"""
新架构测试脚本

测试新架构的各个组件是否正常工作
"""

import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_core_systems():
    """测试核心系统"""
    logger.info("Testing core systems...")
    
    # 测试事件系统
    from .core.event_system import EventSystem, Event, EventType
    
    event_system = EventSystem()
    events_received = []
    
    def test_listener(event):
        events_received.append(event)
    
    event_system.subscribe(EventType.GAME_CREATED, test_listener)
    
    test_event = Event(
        type=EventType.GAME_CREATED,
        source="test",
        data={"test": "data"}
    )
    
    event_system.emit(test_event)
    
    assert len(events_received) == 1
    assert events_received[0].type == EventType.GAME_CREATED
    logger.info("✓ Event system working")
    
    # 测试组件系统
    from .core.component_system import ComponentSystem, Component, Entity
    
    class TestComponent(Component):
        def __init__(self, entity_id):
            super().__init__(entity_id)
            self.value = 42
        
        def update(self, delta_time):
            pass
    
    component_system = ComponentSystem()
    entity = component_system.create_entity()
    
    test_comp = TestComponent(entity.id)
    entity.add_component(test_comp)
    
    retrieved_comp = entity.get_component(TestComponent)
    assert retrieved_comp is not None
    assert retrieved_comp.value == 42
    logger.info("✓ Component system working")
    
    # 测试状态管理
    from .core.state_manager import StateManager, GameState
    
    state_manager = StateManager()
    game_state_machine = state_manager.create_game_state_machine("test_game")
    
    assert game_state_machine.current_state == GameState.IDLE
    
    success = game_state_machine.transition_to(GameState.WAITING)
    assert success
    assert game_state_machine.current_state == GameState.WAITING
    logger.info("✓ State management working")


def test_character_system():
    """测试角色系统"""
    logger.info("Testing character system...")
    
    # 测试角色数据
    from .character.character_data import get_all_characters, get_character_data
    
    characters = get_all_characters()
    assert len(characters) > 0
    logger.info(f"✓ Found {len(characters)} characters")
    
    # 测试角色创建
    from .character.character_factory import CharacterFactory
    
    hiyori_data = get_character_data("hiyori")
    assert hiyori_data is not None
    
    character_entity = CharacterFactory.create_character("hiyori", "test_player")
    assert character_entity is not None
    
    # 测试属性组件
    from .character.components import AttributeComponent
    from .character.character_data import AttributeType
    
    attr_component = character_entity.get_component(AttributeComponent)
    assert attr_component is not None
    
    health = attr_component.get_attribute(AttributeType.CURRENT_HEALTH)
    assert health > 0
    logger.info("✓ Character system working")


def test_skill_system():
    """测试技能系统"""
    logger.info("Testing skill system...")
    
    # 创建测试角色
    from .character.character_factory import CharacterFactory
    from .skill.skill_factory import SkillFactory
    
    caster = CharacterFactory.create_character("hiyori", "player1")
    target = CharacterFactory.create_character("yui", "player2")
    
    assert caster is not None
    assert target is not None
    
    # 测试技能执行
    all_entities = [caster, target]
    result = SkillFactory.execute_skill(caster, "hiyori_normal_attack", all_entities, target)
    
    assert result is not None
    logger.info(f"✓ Skill execution result: {result.success}")
    
    if result.messages:
        logger.info(f"  Messages: {result.messages}")


def test_game_logic():
    """测试游戏逻辑"""
    logger.info("Testing game logic...")
    
    # 测试房间管理
    from .game.room_manager import RoomManager
    
    room_manager = RoomManager()
    room = room_manager.create_room("test_room", "player1")
    
    assert room is not None
    assert room.room_id == "test_room"
    assert room.creator_id == "player1"
    logger.info("✓ Room creation working")
    
    # 测试玩家加入
    success = room_manager.join_room("test_room", "player2", "Player2")
    assert success
    assert len(room.players) == 2
    logger.info("✓ Player joining working")
    
    # 测试角色选择
    success = room.select_character("player1", "hiyori")
    assert success
    
    success = room.select_character("player2", "yui")
    assert success
    logger.info("✓ Character selection working")
    
    # 测试游戏开始
    from .core.state_manager import GameState
    
    room.state_machine.transition_to(GameState.CHARACTER_SELECT)
    success = room.start_game()
    assert success
    assert room.state_machine.current_state == GameState.PLAYING
    logger.info("✓ Game start working")


def test_runway_system():
    """测试跑道系统"""
    logger.info("Testing runway system...")
    
    from .game.room_manager import RoomManager
    from .game.runway_system import RunwaySystem
    
    room_manager = RoomManager()
    room = room_manager.create_room("runway_test", "player1")
    room_manager.join_room("runway_test", "player2", "Player2")
    
    room.select_character("player1", "hiyori")
    room.select_character("player2", "yui")
    
    from .core.state_manager import GameState
    room.state_machine.transition_to(GameState.CHARACTER_SELECT)
    room.start_game()
    
    runway_system = RunwaySystem(room)
    
    # 测试事件触发
    messages = runway_system.trigger_event("player1", 0)
    assert isinstance(messages, list)
    logger.info(f"✓ Runway event triggered: {messages}")


def run_all_tests():
    """运行所有测试"""
    logger.info("Starting PCR Battle New Architecture Tests...")
    
    try:
        test_core_systems()
        test_character_system()
        test_skill_system()
        test_game_logic()
        test_runway_system()
        
        logger.info("🎉 All tests passed! New architecture is working correctly.")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
